/** @type {import('next').NextConfig} */
const path = require('path');
const withPWA = require('next-pwa')({
  dest: 'public',
  register: true,
  skipWaiting: true,
  disable: process.env.NODE_ENV === 'development',
  buildExcludes: [/middleware-manifest.json$/],
  publicExcludes: ['!noprecache/**/*']
});

const nextConfig = withPWA({
  experimental: {
    serverActions: {
      allowedOrigins: ['localhost:3000', 'weednearmedc.com']
    },
    serverComponentsExternalPackages: ['@xenova/transformers'],
  },
  serverExternalPackages: ['@prisma/client', '@xenova/transformers'],
  webpack: (config, { isServer }) => {
    // Handle AI models and transformers
    config.resolve.alias = {
      ...config.resolve.alias,
      '@ai-models': path.resolve(__dirname, 'src/ai-agents/models'),
    };

    // Optimize for AI model loading
    if (!isServer) {
      config.resolve.fallback = {
        ...config.resolve.fallback,
        fs: false,
        path: false,
        crypto: false,
      };
    }

    return config;
  },
  env: {
    AI_MODELS_PATH: '/ai-models',
    ENABLE_AI_OPTIMIZATION: 'true',
  },
  images: {
    remotePatterns: [
      {
        protocol: 'https',
        hostname: 'api.mapbox.com',
      },
      {
        protocol: 'https',
        hostname: '*.supabase.co',
      },
    ],
  },
  async headers() {
    return [
      {
        source: '/(.*)',
        headers: [
          {
            key: 'X-Content-Type-Options',
            value: 'nosniff',
          },
          {
            key: 'X-Frame-Options',
            value: 'DENY',
          },
          {
            key: 'X-XSS-Protection',
            value: '1; mode=block',
          },
          {
            key: 'Referrer-Policy',
            value: 'strict-origin-when-cross-origin',
          },
        ],
      },
    ];
  },
  async redirects() {
    return [
      {
        source: '/dispensary',
        destination: '/shop',
        permanent: true,
      },
      {
        source: '/weed',
        destination: '/shop/flower',
        permanent: true,
      },
    ];
  },
});

module.exports = nextConfig;