# WeedNearMeDC Deployment Guide

## 🚀 Fast & Flexible Deployment Options

This guide covers deploying your high-performance cannabis delivery platform across different hosting environments while maintaining optimal speed and AI functionality.

## 📋 Prerequisites

- Node.js 18+ 
- Database (PostgreSQL recommended)
- Environment variables configured
- Domain name (for production)

## 🎯 Quick Deploy Options

### Option 1: Vercel (Recommended for Speed)

**Best for**: Serverless, global CDN, automatic scaling

```bash
# 1. Install Vercel CLI
npm i -g vercel

# 2. Deploy
vercel

# 3. Configure environment variables in Vercel dashboard
# - NEXT_PUBLIC_SUPABASE_URL
# - SUPABASE_SERVICE_ROLE_KEY
# - DATABASE_URL
```

**Performance Optimizations for Vercel:**
- ✅ Automatic edge caching
- ✅ Image optimization
- ✅ Serverless functions for AI
- ✅ Global CDN distribution

### Option 2: Netlify

**Best for**: JAMstack, form handling, edge functions

```bash
# 1. Build the project
npm run build

# 2. Deploy to Netlify
# Upload dist folder or connect GitHub repo

# 3. Configure environment variables in Netlify dashboard
```

**Performance Optimizations for Netlify:**
- ✅ Edge functions for AI processing
- ✅ Form handling for orders
- ✅ Split testing capabilities
- ✅ Automatic HTTPS

### Option 3: WordPress Integration

**Best for**: Existing WordPress sites, content management

```bash
# 1. Copy WordPress integration files
cp -r src/integrations/wordpress.php /wp-content/plugins/weednearmedc/

# 2. Deploy Next.js app separately (Vercel/Netlify)

# 3. Configure WordPress plugin
# - Set API endpoint to your Next.js deployment
# - Activate plugin in WordPress admin
```

**WordPress Setup:**
1. Upload `wordpress.php` to `/wp-content/plugins/weednearmedc/`
2. Activate plugin in WordPress admin
3. Configure API endpoint in Settings > WeedNearMeDC AI
4. Use shortcodes: `[weed_delivery_info neighborhood="Downtown" city="Washington" state="DC"]`

### Option 4: Traditional VPS/Dedicated Server

**Best for**: Full control, custom configurations

```bash
# 1. Clone repository
git clone <your-repo>
cd weednearmedc

# 2. Install dependencies
npm install

# 3. Build for production
npm run build

# 4. Start with PM2 (recommended)
npm install -g pm2
pm2 start npm --name "weednearmedc" -- start

# 5. Configure reverse proxy (Nginx)
```

## ⚡ Performance Optimization by Platform

### Vercel Optimizations

```javascript
// next.config.js additions for Vercel
module.exports = {
  // Enable experimental features
  experimental: {
    serverComponentsExternalPackages: ['@xenova/transformers']
  },
  
  // Optimize images
  images: {
    domains: ['your-domain.com'],
    formats: ['image/webp', 'image/avif']
  },
  
  // Enable compression
  compress: true,
  
  // Optimize for serverless
  output: 'standalone'
}
```

### WordPress Performance

```php
// Add to WordPress theme functions.php
function weednearmedc_optimize_performance() {
    // Enable caching
    wp_cache_set('weednearmedc_cache_enabled', true);
    
    // Preload critical CSS
    wp_enqueue_style('weednearmedc-critical', 
        plugin_dir_url(__FILE__) . 'critical.css', 
        [], '1.0.0'
    );
    
    // Defer non-critical JS
    wp_enqueue_script('weednearmedc-ai', 
        plugin_dir_url(__FILE__) . 'ai.min.js', 
        [], '1.0.0', true
    );
}
add_action('wp_enqueue_scripts', 'weednearmedc_optimize_performance');
```

## 🔧 Environment Configuration

### Required Environment Variables

```bash
# Database
DATABASE_URL="********************************/db"

# Supabase
NEXT_PUBLIC_SUPABASE_URL="https://your-project.supabase.co"
SUPABASE_SERVICE_ROLE_KEY="your-service-role-key"

# AI Configuration
AI_MODEL_CACHE_DIR="./ai-models-cache"
AI_MAX_CONCURRENT_REQUESTS="5"

# Platform-specific
DEPLOYMENT_PLATFORM="vercel" # or "netlify", "wordpress", "traditional"

# Optional: External APIs
GOOGLE_MAPS_API_KEY="your-google-maps-key"
STRIPE_SECRET_KEY="your-stripe-key"
```

### Platform-Specific Variables

**Vercel:**
```bash
VERCEL_URL="auto-detected"
VERCEL_ENV="production"
```

**Netlify:**
```bash
NETLIFY_SITE_URL="auto-detected"
NETLIFY_DEPLOY_URL="auto-detected"
```

**WordPress:**
```bash
WORDPRESS_URL="https://your-wordpress-site.com"
WP_API_ENDPOINT="/wp-json/weednearmedc/v1"
```

## 🎨 Custom Domain Setup

### Vercel
1. Go to Vercel dashboard > Project > Settings > Domains
2. Add your domain
3. Configure DNS records as shown

### Netlify
1. Go to Netlify dashboard > Site > Domain settings
2. Add custom domain
3. Configure DNS or use Netlify DNS

### WordPress
1. Point domain to WordPress hosting
2. Configure subdomain for API (api.yourdomain.com)
3. Update CORS settings

## 🔒 Security Configuration

### SSL/HTTPS
- ✅ Automatic on Vercel/Netlify
- ⚙️ Configure Let's Encrypt for VPS
- ✅ WordPress hosting usually includes SSL

### CORS Configuration
```javascript
// next.config.js
module.exports = {
  async headers() {
    return [
      {
        source: '/api/:path*',
        headers: [
          { key: 'Access-Control-Allow-Origin', value: 'https://your-wordpress-site.com' },
          { key: 'Access-Control-Allow-Methods', value: 'GET, POST, OPTIONS' },
          { key: 'Access-Control-Allow-Headers', value: 'Content-Type, Authorization' }
        ]
      }
    ]
  }
}
```

## 📊 Monitoring & Analytics

### Performance Monitoring
```javascript
// Add to your deployment
import { deploymentAdapter } from '@/ai-agents/deployment-adapter'

// Monitor performance
setInterval(async () => {
  const health = await deploymentAdapter.healthCheck()
  console.log('Health check:', health)
}, 60000)
```

### Error Tracking
- **Vercel**: Built-in error tracking
- **Netlify**: Integrate with Sentry
- **WordPress**: Use Query Monitor plugin
- **VPS**: Configure log monitoring

## 🚀 Deployment Checklist

### Pre-Deployment
- [ ] Environment variables configured
- [ ] Database schema migrated
- [ ] AI models tested
- [ ] Performance optimized
- [ ] Security configured

### Post-Deployment
- [ ] SSL certificate active
- [ ] Domain configured
- [ ] Performance monitoring active
- [ ] Error tracking configured
- [ ] Backup strategy implemented

### WordPress-Specific
- [ ] Plugin activated
- [ ] API endpoint configured
- [ ] Shortcodes tested
- [ ] Cache configured
- [ ] Performance optimized

## 🔧 Troubleshooting

### Common Issues

**AI Models Not Loading:**
```bash
# Check model cache directory
ls -la ./ai-models-cache/

# Clear cache and restart
rm -rf ./ai-models-cache/
npm run dev
```

**WordPress Integration Issues:**
```php
// Debug WordPress integration
error_log('WeedNearMeDC Debug: ' . print_r($api_response, true));
```

**Performance Issues:**
```javascript
// Check deployment adapter metrics
const metrics = deploymentAdapter.getMetrics()
console.log('Performance metrics:', metrics)
```

## 📞 Support

For deployment support:
1. Check the troubleshooting section
2. Review platform-specific documentation
3. Monitor performance metrics
4. Check error logs

## 🎯 Performance Targets

- **Page Load Time**: < 2 seconds
- **AI Response Time**: < 3 seconds
- **Cache Hit Rate**: > 80%
- **Error Rate**: < 1%
- **Uptime**: > 99.9%

Choose the deployment option that best fits your needs while maintaining these performance targets!
