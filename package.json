{"name": "weednearmedc", "version": "0.1.0", "private": true, "scripts": {"dev": "next dev", "build": "next build", "start": "next start", "lint": "next lint", "test": "jest", "test:e2e": "playwright test", "test:lighthouse": "lhci autorun", "db:generate": "prisma generate", "db:push": "prisma db push", "db:migrate": "prisma migrate dev", "db:seed": "tsx prisma/seed.ts", "db:studio": "prisma studio", "deploy": "vercel --prod", "type-check": "tsc --noEmit", "ai:download-models": "node scripts/download-models.js", "ai:start-workers": "node scripts/start-ai-workers.js", "ai:test": "node -e \"console.log('Testing AI setup...')\"", "postinstall": "prisma generate"}, "dependencies": {"@apollo/client": "^3.13.8", "@apollo/server": "^4.12.2", "@as-integrations/next": "^3.2.0", "@hookform/resolvers": "^5.1.1", "@huggingface/inference": "^4.5.1", "@prisma/client": "^6.11.1", "@radix-ui/react-accordion": "^1.2.11", "@radix-ui/react-alert-dialog": "^1.1.14", "@radix-ui/react-avatar": "^1.1.10", "@radix-ui/react-checkbox": "^1.3.2", "@radix-ui/react-dialog": "^1.1.14", "@radix-ui/react-dropdown-menu": "^2.1.15", "@radix-ui/react-label": "^2.1.7", "@radix-ui/react-navigation-menu": "^1.2.13", "@radix-ui/react-popover": "^1.1.14", "@radix-ui/react-progress": "^1.1.7", "@radix-ui/react-radio-group": "^1.3.7", "@radix-ui/react-select": "^2.2.5", "@radix-ui/react-separator": "^1.1.7", "@radix-ui/react-slider": "^1.3.5", "@radix-ui/react-slot": "^1.2.3", "@radix-ui/react-switch": "^1.2.5", "@radix-ui/react-tabs": "^1.1.12", "@radix-ui/react-toggle": "^1.1.9", "@radix-ui/react-tooltip": "^1.2.7", "@supabase/auth-helpers-nextjs": "^0.10.0", "@supabase/auth-helpers-react": "^0.5.0", "@supabase/ssr": "^0.6.1", "@supabase/supabase-js": "^2.50.5", "@tanstack/react-query": "^5.83.0", "@trpc/client": "^11.4.3", "@trpc/next": "^11.4.3", "@trpc/react-query": "^11.4.3", "@trpc/server": "^11.4.3", "@types/node-cron": "^3.0.11", "@types/uuid": "^10.0.0", "@xenova/transformers": "^2.17.2", "class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "date-fns": "^4.1.0", "framer-motion": "^12.0.0", "graphql": "^16.11.0", "graphql-tag": "^2.12.6", "lucide-react": "^0.525.0", "mapbox-gl": "^3.8.0", "next": "15.3.5", "next-pwa": "^5.6.0", "next-themes": "^0.4.6", "node-cron": "^4.2.1", "prisma": "^6.11.1", "react": "^19.0.0", "react-dom": "^19.0.0", "react-hook-form": "^7.60.0", "react-intersection-observer": "^9.15.0", "react-map-gl": "^7.1.9", "sonner": "^2.0.6", "tailwind-merge": "^3.3.1", "three": "^0.175.0", "uuid": "^11.1.0", "workbox-webpack-plugin": "^7.1.0", "zod": "^4.0.5"}, "devDependencies": {"@eslint/eslintrc": "^3", "@lhci/cli": "^0.14.0", "@playwright/test": "^1.54.1", "@tailwindcss/postcss": "^4", "@testing-library/jest-dom": "^6.6.3", "@testing-library/react": "^16.3.0", "@types/jest": "^30.0.0", "@types/mapbox-gl": "^3.4.0", "@types/node": "^20", "@types/react": "^19", "@types/react-dom": "^19", "@types/three": "^0.175.0", "eslint": "^9", "eslint-config-next": "15.3.5", "jest": "^30.0.4", "jest-environment-jsdom": "^30.0.4", "lighthouse": "^12.3.1", "tailwindcss": "^4", "tsx": "^4.20.3", "tw-animate-css": "^1.3.5", "typescript": "^5"}}