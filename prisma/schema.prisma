// WeedNearMeDC Cannabis Delivery Platform Schema
// Production-ready schema for DC/MD/VA cannabis delivery

generator client {
  provider = "prisma-client-js"
}

datasource db {
  provider = "postgresql"
  url      = env("DATABASE_URL")
}

// User Management & Authentication
model User {
  id        String   @id @default(uuid()) @db.Uuid
  email     String   @unique
  password  String?  // Optional for OAuth users
  firstName String?
  lastName  String?
  phone     String?
  dateOfBirth DateTime?

  // Age verification
  ageVerified <PERSON>olean @default(false)
  idVerified  Boolean @default(false)
  idImageUrl  String?

  // Location & Preferences
  state       String? // DC, MD, VA
  city        String?
  zipCode     String?
  address     String?

  // Account status
  isActive    Boolean @default(true)
  role        UserRole @default(CUSTOMER)

  // Timestamps
  createdAt   DateTime @default(now())
  updatedAt   DateTime @updatedAt
  lastLoginAt DateTime?

  // Relations
  orders      Order[]
  reviews     Review[]
  loyalty     Loyalty?
  cart        CartItem[]
  favorites   Favorite[]

  @@map("users")
}

enum UserRole {
  CUSTOMER
  DRIVER
  ADMIN
  SUPER_ADMIN
}

// Product Catalog
model Product {
  id          Int      @id @default(autoincrement())
  name        String
  slug        String   @unique
  description String?
  category    ProductCategory
  subcategory String?

  // Cannabis-specific fields
  thcContent  String?  // "20-25%"
  cbdContent  String?  // "0-1%"
  strain      String?  // "Indica", "Sativa", "Hybrid"
  effects     String[] // ["relaxing", "euphoric", "creative"]
  flavors     String[] // ["citrus", "earthy", "sweet"]

  // Pricing & Inventory
  price       Int      // Price in cents
  compareAtPrice Int?  // Original price for sales
  weight      String?  // "1g", "3.5g", "7g", "14g", "28g"
  inStock     Boolean  @default(true)
  stockCount  Int      @default(0)

  // SEO & Marketing
  metaTitle       String?
  metaDescription String?
  featured        Boolean @default(false)
  onSale          Boolean @default(false)

  // Media
  images      String[] // Array of image URLs
  videoUrl    String?

  // Ratings & Reviews
  rating      Float    @default(0)
  reviewCount Int      @default(0)

  // Compliance
  labTested   Boolean  @default(false)
  labResults  String?  // URL to lab results

  // State availability
  availableInDC Boolean @default(false)
  availableInMD Boolean @default(true)  // Primary market
  availableInVA Boolean @default(false)

  // Timestamps
  createdAt   DateTime @default(now())
  updatedAt   DateTime @updatedAt

  // AI optimization
  aiOptimized       Boolean  @default(false)
  aiDescription     String?  // AI-generated description
  lastAiUpdate      DateTime?
  optimizationReason String?

  // AI SEO fields
  seoScore          Float?   // 0-100 score
  llmVisibility     Float?   // 0-1 score
  keywordDensity    Float?   // 0-1 score

  // Dynamic pricing
  dynamicPricing    Boolean  @default(false)
  minPrice          Int?     // Minimum price in cents
  maxPrice          Int?     // Maximum price in cents
  competitiveIndex  Float?   // 0-1 score relative to competitors

  // Relations
  orderItems  OrderItem[]
  reviews     Review[]
  cartItems   CartItem[]
  favorites   Favorite[]
  aiContent   AiContent[]

  @@map("products")
}

enum ProductCategory {
  FLOWER
  EDIBLES
  VAPES
  CONCENTRATES
  TOPICALS
  ACCESSORIES
  PRE_ROLLS
  TINCTURES
}

// Shopping Cart
model CartItem {
  id        Int      @id @default(autoincrement())
  userId    String   @db.Uuid
  productId Int
  quantity  Int      @default(1)
  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt

  // Relations
  user      User     @relation(fields: [userId], references: [id], onDelete: Cascade)
  product   Product  @relation(fields: [productId], references: [id], onDelete: Cascade)

  @@unique([userId, productId])
  @@map("cart_items")
}

// User Favorites
model Favorite {
  id        Int      @id @default(autoincrement())
  userId    String   @db.Uuid
  productId Int
  createdAt DateTime @default(now())

  // Relations
  user      User     @relation(fields: [userId], references: [id], onDelete: Cascade)
  product   Product  @relation(fields: [productId], references: [id], onDelete: Cascade)

  @@unique([userId, productId])
  @@map("favorites")
}

// Order Management
model Order {
  id          Int         @id @default(autoincrement())
  userId      String      @db.Uuid
  orderNumber String      @unique

  // Order details
  status      OrderStatus @default(PENDING)
  total       Int         // Total in cents
  subtotal    Int         // Subtotal in cents
  tax         Int         // Tax in cents
  tip         Int         @default(0) // Tip in cents
  deliveryFee Int         @default(0) // Delivery fee in cents

  // Delivery information
  deliveryAddress String
  deliveryCity    String
  deliveryState   String
  deliveryZip     String
  deliveryNotes   String?

  // Driver assignment
  driverId        String?     @db.Uuid
  estimatedDelivery DateTime?
  actualDelivery    DateTime?

  // Payment
  paymentMethod   String?
  paymentStatus   PaymentStatus @default(PENDING)

  // Timestamps
  createdAt       DateTime @default(now())
  updatedAt       DateTime @updatedAt

  // Relations
  user            User @relation(fields: [userId], references: [id])
  items           OrderItem[]

  @@map("orders")
}

enum OrderStatus {
  PENDING
  CONFIRMED
  PREPARING
  OUT_FOR_DELIVERY
  DELIVERED
  CANCELLED
  REFUNDED
}

enum PaymentStatus {
  PENDING
  PROCESSING
  COMPLETED
  FAILED
  REFUNDED
}

// Order Items
model OrderItem {
  id        Int @id @default(autoincrement())
  orderId   Int
  productId Int
  quantity  Int
  price     Int // Price at time of order in cents

  // Relations
  order     Order   @relation(fields: [orderId], references: [id], onDelete: Cascade)
  product   Product @relation(fields: [productId], references: [id])

  @@map("order_items")
}

// Reviews & Ratings
model Review {
  id        Int      @id @default(autoincrement())
  userId    String   @db.Uuid
  productId Int
  rating    Int      // 1-5 stars
  title     String?
  body      String?
  verified  Boolean  @default(false) // Verified buyer
  helpful   Int      @default(0)     // Helpful votes

  // Timestamps
  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt

  // Relations
  user      User     @relation(fields: [userId], references: [id], onDelete: Cascade)
  product   Product  @relation(fields: [productId], references: [id], onDelete: Cascade)

  @@unique([userId, productId]) // One review per user per product
  @@map("reviews")
}

// Loyalty & Gamification
model Loyalty {
  userId    String   @id @db.Uuid
  points    Int      @default(0)
  orders    Int      @default(0)
  tier      LoyaltyTier @default(BRONZE)
  badges    String[] // Array of earned badge IDs

  // CannaDex progress
  strainsDiscovered Int @default(0)
  categoriesExplored Int @default(0)

  // Virtual greenhouse
  greenhouseLevel   Int @default(1)
  plantsOwned       String[] // Array of plant IDs

  // Referrals
  referralCode      String   @unique
  referralsCount    Int      @default(0)

  // Timestamps
  createdAt         DateTime @default(now())
  updatedAt         DateTime @updatedAt

  // Relations
  user              User     @relation(fields: [userId], references: [id], onDelete: Cascade)

  @@map("loyalty")
}

enum LoyaltyTier {
  BRONZE
  SILVER
  GOLD
  PLATINUM
  DIAMOND
}

// AI & Optimization Tables
// GEO-optimized pages for local SEO
model GeoPage {
  id              Int      @id @default(autoincrement())
  neighborhood    String
  city            String
  state           String   // DC, MD, VA
  zipCode         String?

  // SEO content
  title           String?
  metaDescription String?
  keywords        String[] // Array of target keywords
  aiContent       String?  // AI-generated content
  geoSnippet      String?  // Snippet for LLM responses

  // Performance metrics
  confidenceScore     Float?   @default(0)
  llmVisibilityScore  Float?   @default(0)
  searchRanking       Int?
  clickThroughRate    Float?   @default(0)

  // AI optimization tracking
  lastAiUpdate        DateTime?
  aiOptimized         Boolean  @default(false)
  needsOptimization   Boolean  @default(true)
  optimizationReason  String?

  // Timestamps
  createdAt           DateTime @default(now())
  updatedAt           DateTime @updatedAt

  // Relations
  generatedContent    AiContent[]

  @@unique([neighborhood, city, state])
  @@map("geo_pages")
}

// LLM response tracking for market share analysis
model LlmTracking {
  id          Int      @id @default(autoincrement())
  query       String   // Search query tested
  llmSource   String   // "ChatGPT", "Claude", "Gemini", etc.
  mentioned   Boolean  @default(false) // Whether we were mentioned
  position    Int?     // Position in response (1-10)
  context     String?  // Context of mention
  response    String?  // Full LLM response (truncated)

  // Metadata
  testDate    DateTime @default(now())
  location    String?  // Geographic context of test
  deviceType  String?  // "mobile", "desktop"

  @@map("llm_tracking")
}

// AI optimization cycle logs
model AiOptimizationLog {
  id              Int      @id @default(autoincrement())
  cycleType       String   // "full", "geo", "pricing", "llm"
  status          String   // "success", "failed", "partial"
  startedAt       DateTime @default(now())
  completedAt     DateTime?
  duration        Int?     // Duration in seconds

  // Results
  pagesOptimized  Int      @default(0)
  pricesUpdated   Int      @default(0)
  contentGenerated Int     @default(0)
  errorsCount     Int      @default(0)

  // Performance impact
  performanceGain Float?   // Percentage improvement
  llmShareChange  Float?   // Change in LLM mention rate

  // Details
  details         Json?    // Detailed results and metrics
  errorLog        String?  // Error messages if any

  @@map("ai_optimization_logs")
}

// Page analytics for AI optimization decisions
model PageAnalytics {
  id                  Int      @id @default(autoincrement())
  pageId              Int?     // Reference to GeoPage if applicable
  url                 String   @unique
  pageType            String   // "geo", "product", "category", "blog"

  // Traffic metrics
  pageViews           Int      @default(0)
  uniqueVisitors      Int      @default(0)
  bounceRate          Float    @default(0)
  avgTimeOnPage       Int      @default(0) // Seconds

  // SEO metrics
  organicTraffic      Int      @default(0)
  searchImpressions   Int      @default(0)
  searchClicks        Int      @default(0)
  avgSearchPosition   Float    @default(0)

  // LLM metrics
  llmVisibilityScore  Float    @default(0) // 0-1 score
  llmMentions         Int      @default(0)
  llmTrafficShare     Float    @default(0)

  // Conversion metrics
  conversions         Int      @default(0)
  conversionRate      Float    @default(0)
  revenue             Int      @default(0) // In cents

  // AI optimization status
  lastOptimized       DateTime?
  optimizationScore   Float    @default(0)
  needsAttention      Boolean  @default(false)

  // Timestamps
  dateRecorded        DateTime @default(now())
  lastUpdated         DateTime @updatedAt

  @@map("page_analytics")
}

// AI error logs for debugging and monitoring
model AiErrorLog {
  id          Int      @id @default(autoincrement())
  operation   String   // "geo_optimization", "content_generation", etc.
  errorType   String   // "model_load_error", "api_error", etc.
  errorMessage String
  errorStack  String?

  // Context
  modelName   String?
  inputData   Json?

  // Timestamps
  occurredAt  DateTime @default(now())
  resolved    Boolean  @default(false)
  resolvedAt  DateTime?

  @@map("ai_error_logs")
}

// AI-generated content tracking
model AiContent {
  id              Int      @id @default(autoincrement())
  contentType     String   // "geo_snippet", "product_description", "blog_post"
  title           String?
  content         String
  prompt          String?  // Original prompt used

  // AI model info
  modelUsed       String   // "distilgpt2", "flan-t5", etc.
  temperature     Float?
  maxTokens       Int?

  // Quality metrics
  confidenceScore Float    @default(0)
  humanApproved   Boolean  @default(false)
  performanceScore Float?  // Based on engagement/conversions

  // Usage tracking
  timesUsed       Int      @default(0)
  lastUsed        DateTime?

  // Relations
  geoPageId       Int?     // If content is for a geo page
  productId       Int?     // If content is for a product
  geoPage         GeoPage? @relation(fields: [geoPageId], references: [id], onDelete: SetNull)
  product         Product? @relation(fields: [productId], references: [id], onDelete: SetNull)

  // Timestamps
  generatedAt     DateTime @default(now())
  updatedAt       DateTime @updatedAt

  @@map("ai_content")
}

// Competitor tracking for pricing optimization
model CompetitorData {
  id              Int      @id @default(autoincrement())
  competitorName  String
  productName     String
  productCategory String

  // Pricing data
  price           Int      // Price in cents
  originalPrice   Int?     // Original price if on sale
  availability    Boolean  @default(true)

  // Product details
  thcContent      String?
  cbdContent      String?
  weight          String?

  // Market position
  marketPosition  String   // "above", "below", "competitive"
  priceAdvantage  Float?   // Percentage difference

  // Data source
  sourceUrl       String?
  scrapedAt       DateTime @default(now())
  dataQuality     Float    @default(1.0) // 0-1 confidence in data

  @@map("competitor_data")
}
