import { PrismaClient, ProductCategory } from '@prisma/client'

const prisma = new PrismaClient()

const sampleProducts = [
  {
    name: "Blue Dream",
    slug: "blue-dream",
    description: "A balanced hybrid strain that delivers swift symptom relief without heavy sedative effects. Blue Dream has a sweet berry aroma redolent of its Blueberry parent.",
    category: ProductCategory.FLOWER,
    subcategory: "Hybrid",
    thcContent: "18-22%",
    cbdContent: "0.1-0.3%",
    strain: "Hybrid",
    effects: ["Creative", "Euphoric", "Relaxing", "Uplifting"],
    flavors: ["Berry", "Sweet", "Earthy"],
    price: 4500, // $45.00
    compareAtPrice: 5000,
    weight: "3.5g",
    inStock: true,
    stockCount: 25,
    featured: true,
    onSale: true,
    images: ["/images/products/blue-dream.jpg"],
    rating: 4.8,
    reviewCount: 124,
    labTested: true,
    availableInMD: true,
    availableInDC: false,
    availableInVA: false
  },
  {
    name: "<PERSON><PERSON> <PERSON><PERSON>",
    slug: "og-kush",
    description: "A legendary strain with a unique terpene profile that boasts a complex aroma with notes of fuel, skunk, and spice.",
    category: ProductCategory.FLOWER,
    subcategory: "Indica",
    thcContent: "20-25%",
    cbdContent: "0.1-0.2%",
    strain: "Indica",
    effects: ["Relaxing", "Sleepy", "Happy", "Euphoric"],
    flavors: ["Pine", "Earthy", "Diesel"],
    price: 5000, // $50.00
    weight: "3.5g",
    inStock: true,
    stockCount: 18,
    featured: true,
    images: ["/images/products/og-kush.jpg"],
    rating: 4.9,
    reviewCount: 156,
    labTested: true,
    availableInMD: true,
    availableInDC: false,
    availableInVA: false
  },
  {
    name: "Sour Diesel Gummies",
    slug: "sour-diesel-gummies",
    description: "Delicious sour gummies infused with Sour Diesel terpenes. Each gummy contains 10mg of THC for precise dosing.",
    category: ProductCategory.EDIBLES,
    subcategory: "Gummies",
    thcContent: "10mg per piece",
    effects: ["Energizing", "Uplifting", "Focused", "Creative"],
    flavors: ["Sour", "Citrus", "Tropical"],
    price: 3000, // $30.00
    weight: "10mg x 10 pieces",
    inStock: true,
    stockCount: 45,
    images: ["/images/products/sour-gummies.jpg"],
    rating: 4.6,
    reviewCount: 89,
    labTested: true,
    availableInMD: true,
    availableInDC: false,
    availableInVA: false
  },
  {
    name: "Live Resin Vape Cart - Wedding Cake",
    slug: "live-resin-vape-cart-wedding-cake",
    description: "Premium live resin vape cartridge featuring Wedding Cake strain. Full spectrum extract with natural terpenes.",
    category: ProductCategory.VAPES,
    subcategory: "Cartridge",
    thcContent: "85-90%",
    strain: "Indica",
    effects: ["Relaxing", "Happy", "Sleepy", "Euphoric"],
    flavors: ["Sweet", "Vanilla", "Earthy"],
    price: 5500, // $55.00
    weight: "1g",
    inStock: true,
    stockCount: 32,
    featured: true,
    images: ["/images/products/wedding-cake-cart.jpg"],
    rating: 4.7,
    reviewCount: 78,
    labTested: true,
    availableInMD: true,
    availableInDC: false,
    availableInVA: false
  },
  {
    name: "Rosin Concentrate - Gelato",
    slug: "rosin-concentrate-gelato",
    description: "Solventless rosin concentrate made from premium Gelato flower. Rich in terpenes and cannabinoids.",
    category: ProductCategory.CONCENTRATES,
    subcategory: "Rosin",
    thcContent: "75-85%",
    strain: "Hybrid",
    effects: ["Creative", "Relaxing", "Happy", "Euphoric"],
    flavors: ["Sweet", "Fruity", "Dessert"],
    price: 7000, // $70.00
    weight: "1g",
    inStock: true,
    stockCount: 12,
    featured: true,
    images: ["/images/products/gelato-rosin.jpg"],
    rating: 4.9,
    reviewCount: 45,
    labTested: true,
    availableInMD: true,
    availableInDC: false,
    availableInVA: false
  },
  {
    name: "CBD Relief Balm",
    slug: "cbd-relief-balm",
    description: "Soothing topical balm infused with 500mg of CBD and natural essential oils for targeted relief.",
    category: ProductCategory.TOPICALS,
    subcategory: "Balm",
    thcContent: "0%",
    cbdContent: "500mg",
    effects: ["Pain Relief", "Anti-inflammatory", "Soothing"],
    flavors: ["Eucalyptus", "Mint", "Lavender"],
    price: 4000, // $40.00
    weight: "2oz",
    inStock: true,
    stockCount: 28,
    images: ["/images/products/cbd-balm.jpg"],
    rating: 4.5,
    reviewCount: 67,
    labTested: true,
    availableInMD: true,
    availableInDC: true,
    availableInVA: true
  },
  {
    name: "Pre-Roll 5-Pack - Mixed Strains",
    slug: "pre-roll-5-pack-mixed-strains",
    description: "Variety pack of 5 premium pre-rolls featuring different strains. Perfect for trying new varieties.",
    category: ProductCategory.PRE_ROLLS,
    subcategory: "Variety Pack",
    thcContent: "18-25%",
    strain: "Mixed",
    effects: ["Varies by strain"],
    flavors: ["Varies by strain"],
    price: 6000, // $60.00
    weight: "0.5g x 5",
    inStock: true,
    stockCount: 20,
    images: ["/images/products/pre-roll-pack.jpg"],
    rating: 4.4,
    reviewCount: 92,
    labTested: true,
    availableInMD: true,
    availableInDC: false,
    availableInVA: false
  },
  {
    name: "Full Spectrum Tincture - 1000mg",
    slug: "full-spectrum-tincture-1000mg",
    description: "High-potency full spectrum tincture with 1000mg THC. Precise dosing with included dropper.",
    category: ProductCategory.TINCTURES,
    subcategory: "Full Spectrum",
    thcContent: "1000mg total",
    effects: ["Relaxing", "Pain Relief", "Sleep Aid"],
    flavors: ["Natural", "Hemp"],
    price: 8000, // $80.00
    weight: "30ml",
    inStock: true,
    stockCount: 15,
    images: ["/images/products/tincture-1000mg.jpg"],
    rating: 4.6,
    reviewCount: 34,
    labTested: true,
    availableInMD: true,
    availableInDC: false,
    availableInVA: false
  }
]

async function main() {
  console.log('🌱 Seeding database...')

  // Clear existing data
  await prisma.review.deleteMany()
  await prisma.cartItem.deleteMany()
  await prisma.orderItem.deleteMany()
  await prisma.order.deleteMany()
  await prisma.loyalty.deleteMany()
  await prisma.product.deleteMany()
  await prisma.user.deleteMany()

  // Create products
  for (const productData of sampleProducts) {
    await prisma.product.create({
      data: productData
    })
  }

  console.log('✅ Database seeded successfully!')
  console.log(`📦 Created ${sampleProducts.length} products`)
}

main()
  .catch((e) => {
    console.error('❌ Error seeding database:', e)
    process.exit(1)
  })
  .finally(async () => {
    await prisma.$disconnect()
  })
