FROM node:18-alpine

# Install Python for model management
RUN apk add --no-cache python3 py3-pip

# Install model management tools
RUN pip3 install --no-cache-dir transformers torch

WORKDIR /app

# Copy package files
COPY package.json package-lock.json ./

# Install dependencies
RUN npm ci

# Copy AI agents
COPY src/ai-agents ./src/ai-agents
COPY scripts ./scripts

# Download and cache models
RUN node scripts/download-models.js

# Copy the rest of the application
COPY . .

# Set environment variables
ENV NODE_ENV=production
ENV ENABLE_AI_OPTIMIZATION=true

# Run AI orchestrator
CMD ["node", "scripts/start-ai-workers.js"]
