// Mock implementation of @xenova/transformers for testing

export const pipeline = jest.fn((task, model, options) => {
  // Return a mock pipeline function based on the task
  switch (task) {
    case 'text-generation':
      return Promise.resolve(jest.fn((prompt, options) => {
        return Promise.resolve([{
          generated_text: `Mock generated text for: ${prompt.substring(0, 50)}...`
        }])
      }))
    
    case 'text-classification':
      return Promise.resolve(jest.fn((text) => {
        return Promise.resolve([{
          label: 'POSITIVE',
          score: 0.85
        }])
      }))
    
    case 'feature-extraction':
      return Promise.resolve(jest.fn((text) => {
        return Promise.resolve([
          new Array(384).fill(0).map(() => Math.random())
        ])
      }))
    
    default:
      return Promise.resolve(jest.fn(() => Promise.resolve({})))
  }
})

export const env = {
  allowLocalModels: true,
  allowRemoteModels: true,
  useBrowserCache: true,
  useCustomCache: true,
  cacheDir: './test-cache',
  backends: {
    onnx: {
      wasm: {
        numThreads: 1,
        simd: true
      }
    }
  }
}

// Mock model configurations
export const MODEL_CONFIGS = {
  'text-generation': {
    primary: 'mock-distilgpt2',
    fallback: 'mock-gpt2',
    options: {
      quantized: true,
      device: 'cpu',
      dtype: 'fp32'
    }
  },
  'text-classification': {
    primary: 'mock-distilbert',
    fallback: 'mock-bert',
    options: {
      quantized: true,
      device: 'cpu'
    }
  },
  'feature-extraction': {
    primary: 'mock-minilm',
    fallback: 'mock-distilbert',
    options: {
      quantized: true,
      device: 'cpu'
    }
  }
}

// Mock AutoTokenizer
export const AutoTokenizer = {
  from_pretrained: jest.fn(() => Promise.resolve({
    encode: jest.fn((text) => [1, 2, 3, 4, 5]),
    decode: jest.fn((tokens) => 'decoded text'),
    tokenize: jest.fn((text) => ['token1', 'token2', 'token3'])
  }))
}

// Mock AutoModel
export const AutoModel = {
  from_pretrained: jest.fn(() => Promise.resolve({
    generate: jest.fn(() => Promise.resolve([[1, 2, 3, 4, 5]])),
    forward: jest.fn(() => Promise.resolve({
      logits: [[0.1, 0.2, 0.3, 0.4]]
    }))
  }))
}

// Export default
export default {
  pipeline,
  env,
  AutoTokenizer,
  AutoModel,
  MODEL_CONFIGS
}
