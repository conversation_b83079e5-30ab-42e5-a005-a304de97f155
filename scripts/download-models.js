#!/usr/bin/env node

/**
 * This script downloads and caches the AI models used by the WeedNearMeDC platform.
 * It uses the Hugging Face Hub API to download the models and stores them in the
 * src/ai-agents/models directory.
 */

const fs = require('fs');
const path = require('path');
const https = require('https');
const { pipeline } = require('stream');
const { promisify } = require('util');
const streamPipeline = promisify(pipeline);

// Models to download
const MODELS = [
  {
    name: 'geo-optimizer',
    repo: 'Xenova/distilgpt2',
    files: [
      'config.json',
      'tokenizer.json',
      'tokenizer_config.json',
      'vocab.json',
      'merges.txt',
      'pytorch_model.bin'
    ]
  },
  {
    name: 'content-writer',
    repo: 'Xenova/LaMini-Flan-T5-248M',
    files: [
      'config.json',
      'tokenizer.json',
      'tokenizer_config.json',
      'special_tokens_map.json',
      'pytorch_model.bin'
    ]
  },
  {
    name: 'seo-analyzer',
    repo: 'Xenova/distilbert-base-uncased-finetuned-sst-2-english',
    files: [
      'config.json',
      'tokenizer.json',
      'tokenizer_config.json',
      'vocab.txt',
      'pytorch_model.bin'
    ]
  }
];

// Base directory for models
const MODELS_DIR = path.join(__dirname, '..', 'src', 'ai-agents', 'models');

/**
 * Download a file from a URL to a local path
 */
async function downloadFile(url, outputPath) {
  console.log(`Downloading ${url} to ${outputPath}...`);
  
  return new Promise((resolve, reject) => {
    https.get(url, response => {
      if (response.statusCode !== 200) {
        reject(new Error(`Failed to download ${url}: ${response.statusCode} ${response.statusMessage}`));
        return;
      }
      
      const fileStream = fs.createWriteStream(outputPath);
      
      pipeline(response, fileStream, error => {
        if (error) {
          reject(error);
        } else {
          resolve();
        }
      });
    }).on('error', reject);
  });
}

/**
 * Download a model from Hugging Face Hub
 */
async function downloadModel(model) {
  const modelDir = path.join(MODELS_DIR, model.name);
  
  // Create model directory if it doesn't exist
  if (!fs.existsSync(modelDir)) {
    fs.mkdirSync(modelDir, { recursive: true });
  }
  
  console.log(`\n📥 Downloading model: ${model.name} (${model.repo})`);
  
  // Download each file
  for (const file of model.files) {
    const url = `https://huggingface.co/${model.repo}/resolve/main/${file}`;
    const outputPath = path.join(modelDir, file);
    
    // Skip if file already exists
    if (fs.existsSync(outputPath)) {
      console.log(`✅ File already exists: ${file}`);
      continue;
    }
    
    try {
      await downloadFile(url, outputPath);
      console.log(`✅ Downloaded: ${file}`);
    } catch (error) {
      console.error(`❌ Failed to download ${file}:`, error.message);
    }
  }
  
  // Create model info file
  const infoPath = path.join(modelDir, 'model-info.json');
  fs.writeFileSync(infoPath, JSON.stringify({
    name: model.name,
    repo: model.repo,
    files: model.files,
    downloaded_at: new Date().toISOString()
  }, null, 2));
  
  console.log(`✅ Model ${model.name} downloaded successfully`);
}

/**
 * Main function
 */
async function main() {
  console.log('🤖 WeedNearMeDC AI Model Downloader');
  console.log('==================================');
  
  // Create models directory if it doesn't exist
  if (!fs.existsSync(MODELS_DIR)) {
    fs.mkdirSync(MODELS_DIR, { recursive: true });
  }
  
  // Download each model
  for (const model of MODELS) {
    await downloadModel(model);
  }
  
  console.log('\n🎉 All models downloaded successfully!');
  console.log(`📁 Models directory: ${MODELS_DIR}`);
}

// Run the script
main().catch(error => {
  console.error('❌ Error:', error);
  process.exit(1);
});
