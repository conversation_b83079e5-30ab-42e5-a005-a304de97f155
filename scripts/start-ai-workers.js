#!/usr/bin/env node

/**
 * This script starts the AI workers for the WeedNearMeDC platform.
 * It initializes the workers and starts them in the background.
 */

require('dotenv').config();
const { GEOWorker } = require('../src/ai-agents/workers/geo-worker');
const { ContentWorker } = require('../src/ai-agents/workers/content-worker');
const { MonitoringWorker } = require('../src/ai-agents/workers/monitoring-worker');

// Check if AI optimization is enabled
const AI_ENABLED = process.env.ENABLE_AI_OPTIMIZATION === 'true';

if (!AI_ENABLED) {
  console.log('❌ AI optimization is disabled. Set ENABLE_AI_OPTIMIZATION=true to enable.');
  process.exit(0);
}

// Check for required environment variables
const requiredEnvVars = [
  'NEXT_PUBLIC_SUPABASE_URL',
  'SUPABASE_SERVICE_ROLE_KEY'
];

const missingEnvVars = requiredEnvVars.filter(envVar => !process.env[envVar]);

if (missingEnvVars.length > 0) {
  console.error('❌ Missing required environment variables:');
  missingEnvVars.forEach(envVar => console.error(`  - ${envVar}`));
  process.exit(1);
}

/**
 * Main function
 */
async function main() {
  console.log('🤖 WeedNearMeDC AI Workers');
  console.log('=========================');
  
  try {
    // Initialize workers
    console.log('🔄 Initializing workers...');
    
    const geoWorker = new GEOWorker();
    const contentWorker = new ContentWorker();
    const monitoringWorker = new MonitoringWorker();
    
    await Promise.all([
      geoWorker.initialize(),
      contentWorker.initialize(),
      monitoringWorker.initialize()
    ]);
    
    // Start workers
    console.log('🚀 Starting workers...');
    
    await Promise.all([
      geoWorker.start(),
      contentWorker.start(),
      monitoringWorker.start()
    ]);
    
    console.log('✅ All workers started successfully!');
    
    // Keep the process running
    console.log('📊 Workers are running in the background...');
    console.log('Press Ctrl+C to stop the workers');
    
    // Handle process termination
    process.on('SIGINT', async () => {
      console.log('\n🛑 Stopping workers...');
      
      try {
        await Promise.all([
          geoWorker.stop(),
          contentWorker.stop(),
          monitoringWorker.stop()
        ]);
        
        console.log('✅ All workers stopped successfully!');
        process.exit(0);
      } catch (error) {
        console.error('❌ Error stopping workers:', error);
        process.exit(1);
      }
    });
    
  } catch (error) {
    console.error('❌ Error starting workers:', error);
    process.exit(1);
  }
}

// Run the script
main().catch(error => {
  console.error('❌ Error:', error);
  process.exit(1);
});
