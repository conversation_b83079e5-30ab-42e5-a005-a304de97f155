import { FullConfig } from '@playwright/test'

async function globalTeardown(config: FullConfig) {
  console.log('🧹 Starting global teardown for WeedNearMeDC e2e tests...')
  
  try {
    // Cleanup test data if needed
    console.log('🗑️ Cleaning up test data...')
    
    // You could add API calls here to clean up test data
    // For example, removing test users, orders, etc.
    
    console.log('✅ Global teardown completed successfully')
    
  } catch (error) {
    console.error('❌ Global teardown failed:', error)
    // Don't throw here as it would fail the entire test suite
  }
}

export default globalTeardown
