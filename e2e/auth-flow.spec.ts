import { test, expect } from '@playwright/test'

test.describe('Authentication Flow', () => {
  test.beforeEach(async ({ page }) => {
    // Start from the home page
    await page.goto('/')
  })

  test('should display age verification on first visit', async ({ page }) => {
    // Should show age verification modal
    await expect(page.getByText('Age Verification Required')).toBeVisible()
    await expect(page.getByText('I am 21 or older - Continue')).toBeVisible()
    await expect(page.getByText('I am under 21 - Exit')).toBeVisible()
  })

  test('should complete age verification flow', async ({ page }) => {
    // Click continue on age verification
    await page.getByText('I am 21 or older - Continue').click()
    
    // Should show date of birth form
    await expect(page.getByText('Verify Your Age')).toBeVisible()
    await expect(page.getByLabel('Date of Birth')).toBeVisible()
    
    // Enter valid date of birth (21+ years old)
    await page.getByLabel('Date of Birth').fill('1990-01-01')
    
    // Submit age verification
    await page.getByText('Verify Age').click()
    
    // Should show success message
    await expect(page.getByText('Age Verified!')).toBeVisible()
    
    // Should redirect to main app
    await expect(page).toHaveURL(/\/md/)
  })

  test('should reject underage users', async ({ page }) => {
    // Click continue on age verification
    await page.getByText('I am 21 or older - Continue').click()
    
    // Enter underage date of birth
    await page.getByLabel('Date of Birth').fill('2010-01-01')
    
    // Submit age verification
    await page.getByText('Verify Age').click()
    
    // Should show error message
    await expect(page.getByText('You must be 21 years or older to access this site')).toBeVisible()
  })

  test('should navigate to login page', async ({ page }) => {
    // Skip age verification for this test
    if (await page.getByText('Skip for now (Demo mode)').isVisible()) {
      await page.getByText('Skip for now (Demo mode)').click()
    }
    
    // Navigate to login
    await page.goto('/auth/login')
    
    // Should show login form
    await expect(page.getByText('Welcome Back')).toBeVisible()
    await expect(page.getByLabel('Email')).toBeVisible()
    await expect(page.getByLabel('Password')).toBeVisible()
    await expect(page.getByRole('button', { name: 'Sign In' })).toBeVisible()
  })

  test('should show validation errors for invalid login', async ({ page }) => {
    await page.goto('/auth/login')
    
    // Try to submit empty form
    await page.getByRole('button', { name: 'Sign In' }).click()
    
    // Should show validation errors
    await expect(page.getByText('Email is required')).toBeVisible()
    await expect(page.getByText('Password is required')).toBeVisible()
  })

  test('should navigate to register page', async ({ page }) => {
    await page.goto('/auth/login')
    
    // Click register link
    await page.getByText('Create an account').click()
    
    // Should navigate to register page
    await expect(page).toHaveURL(/\/auth\/register/)
    await expect(page.getByText('Create Your Account')).toBeVisible()
  })

  test('should show validation errors for invalid registration', async ({ page }) => {
    await page.goto('/auth/register')
    
    // Try to submit empty form
    await page.getByRole('button', { name: 'Create Account' }).click()
    
    // Should show validation errors
    await expect(page.getByText('First name is required')).toBeVisible()
    await expect(page.getByText('Last name is required')).toBeVisible()
    await expect(page.getByText('Email is required')).toBeVisible()
    await expect(page.getByText('Password is required')).toBeVisible()
  })

  test('should validate password requirements', async ({ page }) => {
    await page.goto('/auth/register')
    
    // Enter weak password
    await page.getByLabel('Password').fill('123')
    await page.getByLabel('Confirm Password').fill('456')
    
    // Should show password validation errors
    await page.getByRole('button', { name: 'Create Account' }).click()
    await expect(page.getByText('Password must be at least 8 characters')).toBeVisible()
    await expect(page.getByText('Passwords do not match')).toBeVisible()
  })

  test('should validate age requirement during registration', async ({ page }) => {
    await page.goto('/auth/register')
    
    // Fill form with underage date
    await page.getByLabel('First Name').fill('Test')
    await page.getByLabel('Last Name').fill('User')
    await page.getByLabel('Email').fill('<EMAIL>')
    await page.getByLabel('Phone').fill('**********')
    await page.getByLabel('Date of Birth').fill('2010-01-01')
    await page.getByLabel('Password').fill('password123')
    await page.getByLabel('Confirm Password').fill('password123')
    
    // Accept terms
    await page.getByLabel('I agree to the Terms of Service').check()
    
    // Submit form
    await page.getByRole('button', { name: 'Create Account' }).click()
    
    // Should show age validation error
    await expect(page.getByText('You must be 21 or older to register')).toBeVisible()
  })

  test('should handle Google OAuth flow', async ({ page }) => {
    await page.goto('/auth/login')
    
    // Click Google sign in button
    const googleButton = page.getByText('Continue with Google')
    await expect(googleButton).toBeVisible()
    
    // Note: In a real test, you would mock the OAuth flow
    // For now, just verify the button is present and clickable
    await expect(googleButton).toBeEnabled()
  })

  test('should navigate between auth pages', async ({ page }) => {
    // Start at login
    await page.goto('/auth/login')
    await expect(page.getByText('Welcome Back')).toBeVisible()
    
    // Go to register
    await page.getByText('Create an account').click()
    await expect(page).toHaveURL(/\/auth\/register/)
    await expect(page.getByText('Create Your Account')).toBeVisible()
    
    // Go back to login
    await page.getByText('Already have an account?').click()
    await expect(page).toHaveURL(/\/auth\/login/)
    await expect(page.getByText('Welcome Back')).toBeVisible()
  })

  test('should be responsive on mobile', async ({ page }) => {
    // Set mobile viewport
    await page.setViewportSize({ width: 375, height: 667 })
    
    await page.goto('/auth/login')
    
    // Should still show all elements on mobile
    await expect(page.getByText('Welcome Back')).toBeVisible()
    await expect(page.getByLabel('Email')).toBeVisible()
    await expect(page.getByLabel('Password')).toBeVisible()
    await expect(page.getByRole('button', { name: 'Sign In' })).toBeVisible()
    
    // Form should be properly sized
    const form = page.locator('form')
    const formBox = await form.boundingBox()
    expect(formBox?.width).toBeLessThanOrEqual(375)
  })

  test('should handle keyboard navigation', async ({ page }) => {
    await page.goto('/auth/login')
    
    // Tab through form elements
    await page.keyboard.press('Tab') // Email field
    await expect(page.getByLabel('Email')).toBeFocused()
    
    await page.keyboard.press('Tab') // Password field
    await expect(page.getByLabel('Password')).toBeFocused()
    
    await page.keyboard.press('Tab') // Sign in button
    await expect(page.getByRole('button', { name: 'Sign In' })).toBeFocused()
    
    // Should be able to submit with Enter
    await page.getByLabel('Email').fill('<EMAIL>')
    await page.getByLabel('Password').fill('password')
    await page.keyboard.press('Enter')
    
    // Should attempt to submit (will show error since it's not a real account)
    await expect(page.getByText('Invalid email or password')).toBeVisible()
  })
})
