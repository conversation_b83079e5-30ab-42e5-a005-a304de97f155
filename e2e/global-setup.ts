import { chromium, FullConfig } from '@playwright/test'

async function globalSetup(config: FullConfig) {
  console.log('🚀 Starting global setup for WeedNearMeDC e2e tests...')
  
  // Launch browser for setup
  const browser = await chromium.launch()
  const page = await browser.newPage()
  
  try {
    // Wait for the development server to be ready
    console.log('⏳ Waiting for development server...')
    await page.goto('http://localhost:3000', { waitUntil: 'networkidle' })
    
    // Check if the app is running correctly
    await page.waitForSelector('body', { timeout: 30000 })
    
    // Verify critical elements are present
    const title = await page.title()
    console.log(`📄 Page title: ${title}`)
    
    // Check if the app loads without errors
    const errors = await page.evaluate(() => {
      const errorElements = document.querySelectorAll('[data-testid="error"]')
      return Array.from(errorElements).map(el => el.textContent)
    })
    
    if (errors.length > 0) {
      console.warn('⚠️ Found errors on page:', errors)
    }
    
    // Setup test data if needed
    console.log('📊 Setting up test data...')
    
    // You could add API calls here to set up test data
    // For example, creating test users, products, etc.
    
    console.log('✅ Global setup completed successfully')
    
  } catch (error) {
    console.error('❌ Global setup failed:', error)
    throw error
  } finally {
    await browser.close()
  }
}

export default globalSetup
