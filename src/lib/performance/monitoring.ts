"use client"

import React from 'react'

// Performance monitoring utilities for WeedNearMeDC
export interface PerformanceMetrics {
  pageLoadTime: number
  firstContentfulPaint: number
  largestContentfulPaint: number
  firstInputDelay: number
  cumulativeLayoutShift: number
  timeToInteractive: number
  totalBlockingTime: number
}

export interface UserExperienceMetrics {
  deviceType: 'mobile' | 'tablet' | 'desktop'
  connectionType: string
  userAgent: string
  screenResolution: string
  viewportSize: string
  timestamp: number
}

class PerformanceMonitor {
  private metrics: Partial<PerformanceMetrics> = {}
  private userMetrics: Partial<UserExperienceMetrics> = {}
  private observers: PerformanceObserver[] = []

  constructor() {
    if (typeof window !== 'undefined') {
      this.initializeMonitoring()
      this.collectUserMetrics()
    }
  }

  private initializeMonitoring() {
    // Core Web Vitals monitoring
    this.observeLCP()
    this.observeFID()
    this.observeCLS()
    this.observeFCP()
    this.observeTTI()
    this.observeTBT()
    
    // Page load time
    this.measurePageLoadTime()
  }

  private observeLCP() {
    if ('PerformanceObserver' in window) {
      const observer = new PerformanceObserver((list) => {
        const entries = list.getEntries()
        const lastEntry = entries[entries.length - 1] as any
        this.metrics.largestContentfulPaint = lastEntry.startTime
      })
      
      observer.observe({ entryTypes: ['largest-contentful-paint'] })
      this.observers.push(observer)
    }
  }

  private observeFID() {
    if ('PerformanceObserver' in window) {
      const observer = new PerformanceObserver((list) => {
        const entries = list.getEntries()
        entries.forEach((entry: any) => {
          if (entry.name === 'first-input') {
            this.metrics.firstInputDelay = entry.processingStart - entry.startTime
          }
        })
      })
      
      observer.observe({ entryTypes: ['first-input'] })
      this.observers.push(observer)
    }
  }

  private observeCLS() {
    if ('PerformanceObserver' in window) {
      let clsValue = 0
      const observer = new PerformanceObserver((list) => {
        const entries = list.getEntries()
        entries.forEach((entry: any) => {
          if (!entry.hadRecentInput) {
            clsValue += entry.value
            this.metrics.cumulativeLayoutShift = clsValue
          }
        })
      })
      
      observer.observe({ entryTypes: ['layout-shift'] })
      this.observers.push(observer)
    }
  }

  private observeFCP() {
    if ('PerformanceObserver' in window) {
      const observer = new PerformanceObserver((list) => {
        const entries = list.getEntries()
        entries.forEach((entry: any) => {
          if (entry.name === 'first-contentful-paint') {
            this.metrics.firstContentfulPaint = entry.startTime
          }
        })
      })
      
      observer.observe({ entryTypes: ['paint'] })
      this.observers.push(observer)
    }
  }

  private observeTTI() {
    // Time to Interactive estimation
    if ('PerformanceObserver' in window) {
      const observer = new PerformanceObserver((list) => {
        const entries = list.getEntries()
        // Simplified TTI calculation
        const navigationEntry = entries.find(entry => entry.entryType === 'navigation') as any
        if (navigationEntry) {
          this.metrics.timeToInteractive = navigationEntry.domInteractive
        }
      })
      
      observer.observe({ entryTypes: ['navigation'] })
      this.observers.push(observer)
    }
  }

  private observeTBT() {
    // Total Blocking Time estimation
    if ('PerformanceObserver' in window) {
      let totalBlockingTime = 0
      const observer = new PerformanceObserver((list) => {
        const entries = list.getEntries()
        entries.forEach((entry: any) => {
          if (entry.duration > 50) {
            totalBlockingTime += entry.duration - 50
            this.metrics.totalBlockingTime = totalBlockingTime
          }
        })
      })
      
      observer.observe({ entryTypes: ['longtask'] })
      this.observers.push(observer)
    }
  }

  private measurePageLoadTime() {
    if (typeof window !== 'undefined' && window.performance) {
      window.addEventListener('load', () => {
        setTimeout(() => {
          const navigation = performance.getEntriesByType('navigation')[0] as any
          if (navigation) {
            this.metrics.pageLoadTime = navigation.loadEventEnd - navigation.fetchStart
          }
        }, 0)
      })
    }
  }

  private collectUserMetrics() {
    if (typeof window === 'undefined') return

    this.userMetrics = {
      deviceType: this.getDeviceType(),
      connectionType: this.getConnectionType(),
      userAgent: navigator.userAgent,
      screenResolution: `${screen.width}x${screen.height}`,
      viewportSize: `${window.innerWidth}x${window.innerHeight}`,
      timestamp: Date.now()
    }
  }

  private getDeviceType(): 'mobile' | 'tablet' | 'desktop' {
    const width = window.innerWidth
    if (width < 768) return 'mobile'
    if (width < 1024) return 'tablet'
    return 'desktop'
  }

  private getConnectionType(): string {
    const connection = (navigator as any).connection || (navigator as any).mozConnection || (navigator as any).webkitConnection
    return connection ? connection.effectiveType || 'unknown' : 'unknown'
  }

  public getMetrics(): PerformanceMetrics & UserExperienceMetrics {
    return {
      ...this.metrics,
      ...this.userMetrics
    } as PerformanceMetrics & UserExperienceMetrics
  }

  public async sendMetrics() {
    const metrics = this.getMetrics()
    
    try {
      // Send to analytics endpoint
      await fetch('/api/analytics/performance', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          metrics,
          url: window.location.href,
          timestamp: Date.now()
        })
      })
    } catch (error) {
      console.warn('Failed to send performance metrics:', error)
    }
  }

  public cleanup() {
    this.observers.forEach(observer => observer.disconnect())
    this.observers = []
  }
}

// Singleton instance
let performanceMonitor: PerformanceMonitor | null = null

export function getPerformanceMonitor(): PerformanceMonitor {
  if (!performanceMonitor) {
    performanceMonitor = new PerformanceMonitor()
  }
  return performanceMonitor
}

// React hook for performance monitoring
export function usePerformanceMonitoring() {
  const monitor = getPerformanceMonitor()
  
  React.useEffect(() => {
    // Send metrics after component mount and page interaction
    const timer = setTimeout(() => {
      monitor.sendMetrics()
    }, 5000) // Send after 5 seconds

    return () => {
      clearTimeout(timer)
    }
  }, [monitor])

  React.useEffect(() => {
    // Send metrics before page unload
    const handleBeforeUnload = () => {
      monitor.sendMetrics()
    }

    window.addEventListener('beforeunload', handleBeforeUnload)
    
    return () => {
      window.removeEventListener('beforeunload', handleBeforeUnload)
      monitor.cleanup()
    }
  }, [monitor])

  return {
    getMetrics: () => monitor.getMetrics(),
    sendMetrics: () => monitor.sendMetrics()
  }
}

// Performance budget checker
export function checkPerformanceBudget(metrics: Partial<PerformanceMetrics>) {
  const budgets = {
    largestContentfulPaint: 2500, // 2.5s
    firstInputDelay: 100, // 100ms
    cumulativeLayoutShift: 0.1, // 0.1
    firstContentfulPaint: 1800, // 1.8s
    timeToInteractive: 3800, // 3.8s
    totalBlockingTime: 200 // 200ms
  }

  const violations: string[] = []

  Object.entries(budgets).forEach(([metric, budget]) => {
    const value = metrics[metric as keyof PerformanceMetrics]
    if (value && value > budget) {
      violations.push(`${metric}: ${value} > ${budget}`)
    }
  })

  return {
    passed: violations.length === 0,
    violations
  }
}
