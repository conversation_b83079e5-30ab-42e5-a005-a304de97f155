import { Metadata } from 'next'

export interface SEOConfig {
  title: string
  description: string
  keywords: string[]
  ogImage?: string
  canonical?: string
  noIndex?: boolean
  structuredData?: any
}

export function generateMetadata(config: SEOConfig): Metadata {
  const {
    title,
    description,
    keywords,
    ogImage = '/images/og-default.jpg',
    canonical,
    noIndex = false,
    structuredData
  } = config

  const baseUrl = process.env.NEXT_PUBLIC_APP_URL || 'https://weednearmedc.com'
  const fullTitle = title.includes('WeedNearMeDC') ? title : `${title} | WeedNearMeDC`

  const metadata: Metadata = {
    title: fullTitle,
    description,
    keywords: keywords.join(', '),
    authors: [{ name: 'WeedNearMeDC' }],
    creator: 'WeedNearMeDC',
    publisher: 'WeedNearMeDC',
    metadataBase: new URL(baseUrl),
    alternates: canonical ? { canonical } : undefined,
    robots: noIndex ? 'noindex,nofollow' : 'index,follow',
    
    openGraph: {
      type: 'website',
      locale: 'en_US',
      url: canonical || baseUrl,
      title: fullTitle,
      description,
      siteName: 'WeedNearMeDC',
      images: [
        {
          url: ogImage,
          width: 1200,
          height: 630,
          alt: title,
        },
      ],
    },
    
    twitter: {
      card: 'summary_large_image',
      title: fullTitle,
      description,
      images: [ogImage],
      creator: '@weednearmedc',
    },
    
    verification: {
      google: process.env.GOOGLE_VERIFICATION_CODE,
    },
    
    other: structuredData ? {
      'application/ld+json': JSON.stringify(structuredData)
    } : undefined,
  }

  return metadata
}

// Cannabis-specific structured data generators
export function generateLocalBusinessSchema(location: {
  name: string
  address: string
  city: string
  state: string
  zipCode: string
  phone?: string
  hours?: string[]
}) {
  return {
    '@context': 'https://schema.org',
    '@type': 'LocalBusiness',
    '@id': `https://weednearmedc.com/locations/${location.city.toLowerCase()}`,
    name: `${location.name} - Cannabis Delivery`,
    description: `Premium cannabis delivery service in ${location.city}, ${location.state}. Fast, discreet, and reliable.`,
    url: `https://weednearmedc.com/locations/${location.city.toLowerCase()}`,
    telephone: location.phone,
    address: {
      '@type': 'PostalAddress',
      streetAddress: location.address,
      addressLocality: location.city,
      addressRegion: location.state,
      postalCode: location.zipCode,
      addressCountry: 'US'
    },
    geo: {
      '@type': 'GeoCoordinates',
      // These would be populated from your geo data
    },
    openingHours: location.hours,
    serviceArea: {
      '@type': 'GeoCircle',
      geoMidpoint: {
        '@type': 'GeoCoordinates',
        // Coordinates for service area center
      },
      geoRadius: '25000' // 25km radius
    },
    hasOfferCatalog: {
      '@type': 'OfferCatalog',
      name: 'Cannabis Products',
      itemListElement: [
        {
          '@type': 'Offer',
          itemOffered: {
            '@type': 'Product',
            name: 'Premium Cannabis Flower',
            category: 'Cannabis'
          }
        },
        {
          '@type': 'Offer',
          itemOffered: {
            '@type': 'Product',
            name: 'Cannabis Edibles',
            category: 'Cannabis'
          }
        },
        {
          '@type': 'Offer',
          itemOffered: {
            '@type': 'Product',
            name: 'Cannabis Concentrates',
            category: 'Cannabis'
          }
        }
      ]
    }
  }
}

export function generateProductSchema(product: {
  name: string
  description: string
  price: number
  currency: string
  availability: string
  brand?: string
  category: string
}) {
  return {
    '@context': 'https://schema.org',
    '@type': 'Product',
    name: product.name,
    description: product.description,
    brand: {
      '@type': 'Brand',
      name: product.brand || 'WeedNearMeDC'
    },
    category: product.category,
    offers: {
      '@type': 'Offer',
      price: product.price,
      priceCurrency: product.currency,
      availability: `https://schema.org/${product.availability}`,
      seller: {
        '@type': 'Organization',
        name: 'WeedNearMeDC'
      }
    }
  }
}

export function generateWebsiteSchema() {
  return {
    '@context': 'https://schema.org',
    '@type': 'WebSite',
    '@id': 'https://weednearmedc.com/#website',
    url: 'https://weednearmedc.com',
    name: 'WeedNearMeDC',
    description: 'Premium cannabis delivery service in Washington DC and surrounding areas',
    publisher: {
      '@type': 'Organization',
      '@id': 'https://weednearmedc.com/#organization'
    },
    potentialAction: {
      '@type': 'SearchAction',
      target: {
        '@type': 'EntryPoint',
        urlTemplate: 'https://weednearmedc.com/search?q={search_term_string}'
      },
      'query-input': 'required name=search_term_string'
    }
  }
}

export function generateOrganizationSchema() {
  return {
    '@context': 'https://schema.org',
    '@type': 'Organization',
    '@id': 'https://weednearmedc.com/#organization',
    name: 'WeedNearMeDC',
    url: 'https://weednearmedc.com',
    logo: 'https://weednearmedc.com/images/logo.png',
    description: 'Premium cannabis delivery service in Washington DC',
    contactPoint: {
      '@type': 'ContactPoint',
      contactType: 'customer service',
      availableLanguage: 'English'
    },
    sameAs: [
      // Add social media URLs when available
    ]
  }
}
