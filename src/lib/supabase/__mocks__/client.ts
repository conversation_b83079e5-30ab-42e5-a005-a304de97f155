// Mock implementation of Supabase client for testing

export const supabase = {
  auth: {
    getSession: jest.fn(() => Promise.resolve({ 
      data: { session: null }, 
      error: null 
    })),
    signInWithPassword: jest.fn(() => Promise.resolve({ 
      data: { user: null, session: null }, 
      error: null 
    })),
    signUp: jest.fn(() => Promise.resolve({ 
      data: { user: null, session: null }, 
      error: null 
    })),
    signOut: jest.fn(() => Promise.resolve({ 
      error: null 
    })),
    onAuthStateChange: jest.fn(() => ({ 
      data: { 
        subscription: { 
          unsubscribe: jest.fn() 
        } 
      } 
    })),
    signInWithOAuth: jest.fn(() => Promise.resolve({ 
      error: null 
    })),
    resend: jest.fn(() => Promise.resolve({ 
      error: null 
    })),
  },
  from: jest.fn(() => ({
    select: jest.fn().mockReturnThis(),
    insert: jest.fn().mockReturnThis(),
    update: jest.fn().mockReturnThis(),
    delete: jest.fn().mockReturnThis(),
    eq: jest.fn().mockReturnThis(),
    single: jest.fn(() => Promise.resolve({ 
      data: null, 
      error: null 
    })),
    limit: jest.fn().mockReturnThis(),
    order: jest.fn().mockReturnThis(),
    or: jest.fn().mockReturnThis(),
    lt: jest.fn().mockReturnThis(),
    gt: jest.fn().mockReturnThis(),
  })),
  storage: {
    from: jest.fn(() => ({
      upload: jest.fn(() => Promise.resolve({ 
        error: null 
      })),
      download: jest.fn(() => Promise.resolve({ 
        data: null, 
        error: null 
      })),
    })),
  },
}
