<?php
/**
 * WeedNearMeDC WordPress Integration
 * High-performance AI-powered cannabis delivery content
 * 
 * @package WeedNearMeDC
 * @version 1.0.0
 */

// Prevent direct access
if (!defined('ABSPATH')) {
    exit;
}

class WeedNearMeDC_WordPress_Integration {
    
    private $api_endpoint;
    private $cache_prefix = 'weednearmedc_';
    private $cache_duration = 1800; // 30 minutes
    
    public function __construct() {
        $this->api_endpoint = get_option('weednearmedc_api_endpoint', 'https://your-nextjs-app.vercel.app');
        
        // Initialize WordPress hooks
        add_action('init', array($this, 'init'));
        add_action('wp_enqueue_scripts', array($this, 'enqueue_scripts'));
        add_action('rest_api_init', array($this, 'register_rest_routes'));
        add_action('admin_menu', array($this, 'add_admin_menu'));
        
        // Register shortcodes
        add_shortcode('weed_delivery_info', array($this, 'delivery_info_shortcode'));
        add_shortcode('cannabis_products', array($this, 'products_shortcode'));
        add_shortcode('dispensary_hours', array($this, 'hours_shortcode'));
        
        // WordPress cron for AI optimization
        add_action('weednearmedc_ai_optimization', array($this, 'run_ai_optimization'));
        
        // Schedule AI optimization if not already scheduled
        if (!wp_next_scheduled('weednearmedc_ai_optimization')) {
            wp_schedule_event(time(), 'hourly', 'weednearmedc_ai_optimization');
        }
    }
    
    public function init() {
        // Register custom post types for cannabis products
        $this->register_product_post_type();
        
        // Add meta boxes for AI-generated content
        add_action('add_meta_boxes', array($this, 'add_ai_meta_boxes'));
        add_action('save_post', array($this, 'save_ai_meta_data'));
    }
    
    public function enqueue_scripts() {
        // Enqueue optimized scripts for fast loading
        wp_enqueue_script(
            'weednearmedc-ai',
            plugin_dir_url(__FILE__) . 'assets/weednearmedc-ai.min.js',
            array('jquery'),
            '1.0.0',
            true
        );
        
        // Localize script with AJAX endpoint
        wp_localize_script('weednearmedc-ai', 'weednearmedc_ajax', array(
            'ajax_url' => admin_url('admin-ajax.php'),
            'nonce' => wp_create_nonce('weednearmedc_nonce'),
            'api_endpoint' => $this->api_endpoint
        ));
        
        // Enqueue optimized CSS
        wp_enqueue_style(
            'weednearmedc-style',
            plugin_dir_url(__FILE__) . 'assets/weednearmedc.min.css',
            array(),
            '1.0.0'
        );
    }
    
    /**
     * Main shortcode for delivery information
     */
    public function delivery_info_shortcode($atts) {
        $atts = shortcode_atts(array(
            'neighborhood' => 'Downtown',
            'city' => 'Washington',
            'state' => 'DC',
            'cache' => 'true'
        ), $atts);
        
        // Check cache first for instant loading
        $cache_key = $this->cache_prefix . md5(serialize($atts));
        
        if ($atts['cache'] === 'true') {
            $cached_content = get_transient($cache_key);
            if ($cached_content !== false) {
                return $this->format_delivery_info($cached_content, true);
            }
        }
        
        // Generate AI content
        $ai_content = $this->get_ai_delivery_info($atts);
        
        if ($ai_content && $atts['cache'] === 'true') {
            set_transient($cache_key, $ai_content, $this->cache_duration);
        }
        
        return $this->format_delivery_info($ai_content ?: $this->get_fallback_content($atts), false);
    }
    
    /**
     * Get AI-generated delivery information
     */
    private function get_ai_delivery_info($location) {
        $response = wp_remote_post($this->api_endpoint . '/api/ai/geo-optimize', array(
            'timeout' => 10,
            'headers' => array('Content-Type' => 'application/json'),
            'body' => json_encode(array(
                'type' => 'optimize-page',
                'data' => $location
            ))
        ));
        
        if (is_wp_error($response)) {
            error_log('WeedNearMeDC AI API Error: ' . $response->get_error_message());
            return false;
        }
        
        $body = wp_remote_retrieve_body($response);
        $data = json_decode($body, true);
        
        if ($data && $data['success'] && isset($data['content'])) {
            return $data['content'];
        }
        
        return false;
    }
    
    /**
     * Format delivery information for display
     */
    private function format_delivery_info($content, $cached = false) {
        if (is_array($content)) {
            $text = $content['snippet'] ?? $content;
        } else {
            $text = $content;
        }
        
        $cache_indicator = $cached ? '<small class="weednearmedc-cached">⚡ Instant</small>' : '';
        
        return sprintf(
            '<div class="weednearmedc-delivery-info">
                <div class="weednearmedc-content">%s</div>
                %s
                <div class="weednearmedc-cta">
                    <a href="#order" class="weednearmedc-button">Order Now</a>
                </div>
            </div>',
            wp_kses_post($text),
            $cache_indicator
        );
    }
    
    /**
     * Fallback content for when AI is unavailable
     */
    private function get_fallback_content($location) {
        $state_info = array(
            'DC' => array('legal' => 'medical with self-certification', 'time' => '2-4 hours', 'min' => '$50'),
            'MD' => array('legal' => 'recreational and medical', 'time' => '3-5 hours', 'min' => '$75'),
            'VA' => array('legal' => 'medical only', 'time' => 'next day', 'min' => '$100')
        );
        
        $info = $state_info[$location['state']] ?? $state_info['DC'];
        
        return sprintf(
            'Cannabis delivery is available in %s, %s. We offer %s delivery for %s customers. Minimum order %s. Must be 21+ with valid ID.',
            $location['neighborhood'],
            $location['city'],
            $info['time'],
            $info['legal'],
            $info['min']
        );
    }
    
    /**
     * Register REST API routes
     */
    public function register_rest_routes() {
        register_rest_route('weednearmedc/v1', '/delivery-info', array(
            'methods' => 'GET',
            'callback' => array($this, 'rest_delivery_info'),
            'permission_callback' => '__return_true'
        ));
        
        register_rest_route('weednearmedc/v1', '/clear-cache', array(
            'methods' => 'POST',
            'callback' => array($this, 'rest_clear_cache'),
            'permission_callback' => array($this, 'check_admin_permission')
        ));
    }
    
    /**
     * REST API endpoint for delivery info
     */
    public function rest_delivery_info($request) {
        $params = $request->get_params();
        $content = $this->get_ai_delivery_info($params);
        
        return rest_ensure_response(array(
            'success' => !empty($content),
            'content' => $content,
            'cached' => false
        ));
    }
    
    /**
     * Clear AI content cache
     */
    public function rest_clear_cache($request) {
        global $wpdb;
        
        $wpdb->query(
            $wpdb->prepare(
                "DELETE FROM {$wpdb->options} WHERE option_name LIKE %s",
                '_transient_' . $this->cache_prefix . '%'
            )
        );
        
        return rest_ensure_response(array(
            'success' => true,
            'message' => 'Cache cleared successfully'
        ));
    }
    
    /**
     * Add admin menu
     */
    public function add_admin_menu() {
        add_options_page(
            'WeedNearMeDC AI Settings',
            'WeedNearMeDC AI',
            'manage_options',
            'weednearmedc-ai',
            array($this, 'admin_page')
        );
    }
    
    /**
     * Admin page for AI settings
     */
    public function admin_page() {
        if (isset($_POST['submit'])) {
            update_option('weednearmedc_api_endpoint', sanitize_url($_POST['api_endpoint']));
            echo '<div class="notice notice-success"><p>Settings saved!</p></div>';
        }
        
        $api_endpoint = get_option('weednearmedc_api_endpoint', '');
        
        echo '<div class="wrap">
            <h1>WeedNearMeDC AI Settings</h1>
            <form method="post">
                <table class="form-table">
                    <tr>
                        <th scope="row">API Endpoint</th>
                        <td>
                            <input type="url" name="api_endpoint" value="' . esc_attr($api_endpoint) . '" class="regular-text" />
                            <p class="description">Your Next.js application URL (e.g., https://your-app.vercel.app)</p>
                        </td>
                    </tr>
                </table>
                ' . wp_nonce_field('weednearmedc_settings', 'weednearmedc_nonce') . '
                <p class="submit">
                    <input type="submit" name="submit" class="button-primary" value="Save Settings" />
                    <button type="button" class="button" onclick="clearAICache()">Clear AI Cache</button>
                </p>
            </form>
            
            <script>
            function clearAICache() {
                fetch("' . rest_url('weednearmedc/v1/clear-cache') . '", {
                    method: "POST",
                    headers: {"X-WP-Nonce": "' . wp_create_nonce('wp_rest') . '"}
                }).then(() => alert("Cache cleared!"));
            }
            </script>
        </div>';
    }
    
    /**
     * Run AI optimization via WordPress cron
     */
    public function run_ai_optimization() {
        $response = wp_remote_post($this->api_endpoint . '/api/ai/geo-optimize', array(
            'timeout' => 30,
            'headers' => array('Content-Type' => 'application/json'),
            'body' => json_encode(array('type' => 'full-optimization'))
        ));
        
        if (!is_wp_error($response)) {
            error_log('WeedNearMeDC: AI optimization completed');
        }
    }
    
    public function check_admin_permission() {
        return current_user_can('manage_options');
    }
}

// Initialize the plugin
new WeedNearMeDC_WordPress_Integration();

// Activation hook
register_activation_hook(__FILE__, function() {
    // Create database tables if needed
    flush_rewrite_rules();
});

// Deactivation hook
register_deactivation_hook(__FILE__, function() {
    wp_clear_scheduled_hook('weednearmedc_ai_optimization');
});
?>
