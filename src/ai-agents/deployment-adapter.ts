/**
 * Deployment Adapter for WeedNearMeDC AI System
 * Handles different deployment environments with optimal performance
 * Supports: Vercel, Netlify, WordPress, Traditional hosting
 */

interface DeploymentConfig {
  platform: 'vercel' | 'netlify' | 'wordpress' | 'traditional' | 'auto';
  apiEndpoint?: string;
  cacheStrategy: 'memory' | 'redis' | 'file' | 'database';
  enableWorkers: boolean;
  maxConcurrentRequests: number;
  timeoutMs: number;
}

interface PerformanceMetrics {
  responseTime: number;
  cacheHitRate: number;
  errorRate: number;
  throughput: number;
  memoryUsage: number;
}

export class DeploymentAdapter {
  private config: DeploymentConfig;
  private metrics: PerformanceMetrics;
  private requestQueue: Array<() => Promise<any>> = [];
  private activeRequests = 0;
  
  constructor(config?: Partial<DeploymentConfig>) {
    this.config = {
      platform: 'auto',
      cacheStrategy: 'memory',
      enableWorkers: true,
      maxConcurrentRequests: 5,
      timeoutMs: 10000,
      ...config
    };
    
    this.metrics = {
      responseTime: 0,
      cacheHitRate: 0,
      errorRate: 0,
      throughput: 0,
      memoryUsage: 0
    };
    
    this.detectPlatform();
    this.optimizeForPlatform();
  }
  
  private detectPlatform(): void {
    if (this.config.platform !== 'auto') return;
    
    // Auto-detect deployment platform
    if (process.env.VERCEL) {
      this.config.platform = 'vercel';
    } else if (process.env.NETLIFY) {
      this.config.platform = 'netlify';
    } else if (process.env.WORDPRESS_ENV || process.env.WP_ENV) {
      this.config.platform = 'wordpress';
    } else {
      this.config.platform = 'traditional';
    }
    
    console.log(`🚀 Detected platform: ${this.config.platform}`);
  }
  
  private optimizeForPlatform(): void {
    switch (this.config.platform) {
      case 'vercel':
        // Optimize for Vercel's serverless functions
        this.config.maxConcurrentRequests = 10;
        this.config.timeoutMs = 25000; // Vercel timeout is 30s
        this.config.cacheStrategy = 'memory';
        this.config.enableWorkers = false; // Use Vercel Cron instead
        break;
        
      case 'netlify':
        // Optimize for Netlify Functions
        this.config.maxConcurrentRequests = 8;
        this.config.timeoutMs = 10000; // Netlify timeout is 10s
        this.config.cacheStrategy = 'memory';
        this.config.enableWorkers = false;
        break;
        
      case 'wordpress':
        // Optimize for WordPress hosting
        this.config.maxConcurrentRequests = 3;
        this.config.timeoutMs = 30000;
        this.config.cacheStrategy = 'database'; // Use WordPress database
        this.config.enableWorkers = true;
        break;
        
      case 'traditional':
        // Traditional VPS/dedicated server
        this.config.maxConcurrentRequests = 15;
        this.config.timeoutMs = 60000;
        this.config.cacheStrategy = 'file';
        this.config.enableWorkers = true;
        break;
    }
  }
  
  /**
   * Execute AI request with platform-specific optimizations
   */
  async executeAIRequest<T>(
    requestFn: () => Promise<T>,
    priority: 'high' | 'normal' | 'low' = 'normal'
  ): Promise<T> {
    return new Promise((resolve, reject) => {
      const wrappedRequest = async () => {
        const startTime = Date.now();
        this.activeRequests++;
        
        try {
          const result = await Promise.race([
            requestFn(),
            this.createTimeoutPromise()
          ]);
          
          this.updateMetrics(Date.now() - startTime, true);
          resolve(result);
        } catch (error) {
          this.updateMetrics(Date.now() - startTime, false);
          reject(error);
        } finally {
          this.activeRequests--;
          this.processQueue();
        }
      };
      
      // Queue management based on platform limits
      if (this.activeRequests >= this.config.maxConcurrentRequests) {
        if (priority === 'high') {
          this.requestQueue.unshift(wrappedRequest);
        } else {
          this.requestQueue.push(wrappedRequest);
        }
      } else {
        wrappedRequest();
      }
    });
  }
  
  private createTimeoutPromise(): Promise<never> {
    return new Promise((_, reject) => {
      setTimeout(() => {
        reject(new Error(`Request timeout after ${this.config.timeoutMs}ms`));
      }, this.config.timeoutMs);
    });
  }
  
  private processQueue(): void {
    if (this.requestQueue.length > 0 && this.activeRequests < this.config.maxConcurrentRequests) {
      const nextRequest = this.requestQueue.shift();
      if (nextRequest) {
        nextRequest();
      }
    }
  }
  
  private updateMetrics(responseTime: number, success: boolean): void {
    this.metrics.responseTime = (this.metrics.responseTime + responseTime) / 2;
    this.metrics.errorRate = success ? this.metrics.errorRate * 0.95 : this.metrics.errorRate * 1.05;
    this.metrics.throughput++;
  }
  
  /**
   * Get platform-specific cache implementation
   */
  getCacheImplementation(): any {
    switch (this.config.cacheStrategy) {
      case 'memory':
        return new Map(); // Simple in-memory cache
        
      case 'database':
        // WordPress database cache
        return {
          get: async (key: string) => {
            // Implementation would use WordPress transients API
            return null;
          },
          set: async (key: string, value: any, ttl: number) => {
            // Implementation would use WordPress transients API
          }
        };
        
      case 'file':
        // File-based cache for traditional hosting
        return {
          get: async (key: string) => {
            // Implementation would use filesystem
            return null;
          },
          set: async (key: string, value: any, ttl: number) => {
            // Implementation would use filesystem
          }
        };
        
      default:
        return new Map();
    }
  }
  
  /**
   * Get deployment-specific configuration for AI models
   */
  getAIModelConfig(): any {
    const baseConfig = {
      quantized: true,
      device: 'cpu',
      dtype: 'fp32'
    };
    
    switch (this.config.platform) {
      case 'vercel':
      case 'netlify':
        // Serverless environments need smaller models
        return {
          ...baseConfig,
          maxMemory: '512MB',
          preferredModels: ['distilgpt2', 'distilbert-base-uncased'],
          batchSize: 1
        };
        
      case 'wordpress':
        // WordPress hosting often has limited resources
        return {
          ...baseConfig,
          maxMemory: '256MB',
          preferredModels: ['distilgpt2'],
          batchSize: 1
        };
        
      case 'traditional':
        // Traditional hosting can handle larger models
        return {
          ...baseConfig,
          maxMemory: '2GB',
          preferredModels: ['gpt2', 'bert-base-uncased'],
          batchSize: 4
        };
        
      default:
        return baseConfig;
    }
  }
  
  /**
   * Get performance metrics
   */
  getMetrics(): PerformanceMetrics & { platform: string; config: DeploymentConfig } {
    return {
      ...this.metrics,
      platform: this.config.platform,
      config: this.config
    };
  }
  
  /**
   * WordPress-specific helpers
   */
  getWordPressIntegration() {
    if (this.config.platform !== 'wordpress') {
      return null;
    }
    
    return {
      // WordPress REST API endpoint
      registerRestRoute: () => {
        // Implementation would register WordPress REST API routes
        console.log('📝 Registering WordPress REST API routes for AI');
      },
      
      // WordPress shortcode
      registerShortcode: () => {
        // Implementation would register WordPress shortcodes
        console.log('📝 Registering WordPress shortcodes for AI content');
      },
      
      // WordPress admin interface
      addAdminMenu: () => {
        // Implementation would add WordPress admin menu
        console.log('📝 Adding WordPress admin menu for AI management');
      },
      
      // WordPress cron jobs
      scheduleAITasks: () => {
        // Implementation would use WordPress cron
        console.log('📝 Scheduling AI tasks with WordPress cron');
      }
    };
  }
  
  /**
   * Health check optimized for platform
   */
  async healthCheck(): Promise<{
    healthy: boolean;
    platform: string;
    metrics: PerformanceMetrics;
    recommendations: string[];
  }> {
    const recommendations: string[] = [];
    
    // Platform-specific health checks
    if (this.metrics.responseTime > 5000) {
      recommendations.push('Consider upgrading to a faster hosting plan');
    }
    
    if (this.metrics.errorRate > 0.1) {
      recommendations.push('High error rate detected - check AI model availability');
    }
    
    if (this.config.platform === 'wordpress' && this.metrics.memoryUsage > 80) {
      recommendations.push('WordPress memory limit may be too low for AI operations');
    }
    
    return {
      healthy: this.metrics.errorRate < 0.1 && this.metrics.responseTime < 10000,
      platform: this.config.platform,
      metrics: this.metrics,
      recommendations
    };
  }
}

// Singleton instance
export const deploymentAdapter = new DeploymentAdapter();

// Export platform-specific optimizations
export const platformOptimizations = {
  vercel: {
    enableEdgeRuntime: false, // AI models too large for edge
    useServerlessFunction: true,
    cacheHeaders: { 'Cache-Control': 'public, s-maxage=300' }
  },
  
  netlify: {
    useNetlifyFunctions: true,
    enableBackgroundFunctions: true,
    cacheHeaders: { 'Cache-Control': 'public, max-age=300' }
  },
  
  wordpress: {
    useTransients: true,
    enableWPCron: true,
    adminCapability: 'manage_options'
  }
};
