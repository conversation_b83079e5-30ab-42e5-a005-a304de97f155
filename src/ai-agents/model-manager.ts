import { pipeline, env } from '@xenova/transformers';

// Configure transformers for optimal performance
env.allowLocalModels = true;
env.allowRemoteModels = true;
env.useBrowserCache = true;

// Model configurations optimized for cannabis delivery platform
export const MODEL_CONFIGS = {
  'text-generation': {
    primary: 'Xenova/distilgpt2',
    fallback: 'Xenova/gpt2',
    options: {
      quantized: true,
      device: 'cpu',
      dtype: 'fp32'
    }
  },
  'text-classification': {
    primary: 'Xenova/distilbert-base-uncased-finetuned-sst-2-english',
    fallback: 'Xenova/bert-base-uncased',
    options: {
      quantized: true,
      device: 'cpu'
    }
  },
  'feature-extraction': {
    primary: 'Xenova/all-MiniLM-L6-v2',
    fallback: 'Xenova/distilbert-base-uncased',
    options: {
      quantized: true,
      device: 'cpu'
    }
  }
} as const;

export interface ModelInfo {
  id: string;
  task: keyof typeof MODEL_CONFIGS;
  pipeline: any;
  modelName: string;
  loadedAt: Date;
  lastUsed: Date;
  usageCount: number;
  memoryUsage?: number;
  status: 'loading' | 'ready' | 'error' | 'unloaded';
  error?: string;
}

export class AIModelManager {
  private models: Map<string, ModelInfo> = new Map();
  private loadingPromises: Map<string, Promise<any>> = new Map();
  private maxModels = 3; // Limit concurrent models to manage memory
  private isInitialized = false;

  constructor() {
    this.setupCleanup();
  }

  async initialize(): Promise<void> {
    if (this.isInitialized) return;

    console.log('🤖 Initializing AI Model Manager...');
    
    try {
      // Pre-load essential models
      await this.preloadEssentialModels();
      this.isInitialized = true;
      console.log('✅ AI Model Manager initialized successfully');
    } catch (error) {
      console.error('❌ Failed to initialize AI Model Manager:', error);
      throw error;
    }
  }

  private async preloadEssentialModels(): Promise<void> {
    const essentialModels = [
      { id: 'geo-optimizer', task: 'text-generation' as const },
      { id: 'content-writer', task: 'text-generation' as const }
    ];

    const loadPromises = essentialModels.map(({ id, task }) => 
      this.loadModel(id, task).catch(error => {
        console.warn(`⚠️ Failed to preload ${id}:`, error.message);
        return null;
      })
    );

    await Promise.allSettled(loadPromises);
  }

  async loadModel(modelId: string, task: keyof typeof MODEL_CONFIGS): Promise<any> {
    // Check if model is already loaded
    const existingModel = this.models.get(modelId);
    if (existingModel?.status === 'ready') {
      existingModel.lastUsed = new Date();
      existingModel.usageCount++;
      return existingModel.pipeline;
    }

    // Check if model is currently loading
    if (this.loadingPromises.has(modelId)) {
      return this.loadingPromises.get(modelId);
    }

    // Start loading the model
    const loadPromise = this.doLoadModel(modelId, task);
    this.loadingPromises.set(modelId, loadPromise);

    try {
      const pipeline = await loadPromise;
      this.loadingPromises.delete(modelId);
      return pipeline;
    } catch (error) {
      this.loadingPromises.delete(modelId);
      throw error;
    }
  }

  private async doLoadModel(modelId: string, task: keyof typeof MODEL_CONFIGS): Promise<any> {
    console.log(`🔄 Loading model: ${modelId} (${task})`);

    // Check memory limits
    await this.enforceMemoryLimits();

    const config = MODEL_CONFIGS[task];
    const modelInfo: ModelInfo = {
      id: modelId,
      task,
      pipeline: null,
      modelName: config.primary,
      loadedAt: new Date(),
      lastUsed: new Date(),
      usageCount: 0,
      status: 'loading'
    };

    this.models.set(modelId, modelInfo);

    try {
      // Try primary model first
      let loadedPipeline;
      try {
        loadedPipeline = await pipeline(task, config.primary, {
          ...config.options,
          progress_callback: (progress: any) => {
            if (progress.progress) {
              console.log(`📥 ${modelId}: ${Math.round(progress.progress * 100)}%`);
            }
          }
        });
      } catch (primaryError) {
        console.warn(`⚠️ Primary model failed for ${modelId}, trying fallback...`);
        
        // Try fallback model
        loadedPipeline = await pipeline(task, config.fallback, {
          ...config.options,
          progress_callback: (progress: any) => {
            if (progress.progress) {
              console.log(`📥 ${modelId} (fallback): ${Math.round(progress.progress * 100)}%`);
            }
          }
        });
        
        modelInfo.modelName = config.fallback;
      }

      modelInfo.pipeline = loadedPipeline;
      modelInfo.status = 'ready';
      modelInfo.usageCount = 1;

      console.log(`✅ Model loaded successfully: ${modelId} (${modelInfo.modelName})`);
      return loadedPipeline;

    } catch (error) {
      console.error(`❌ Failed to load model ${modelId}:`, error);
      modelInfo.status = 'error';
      modelInfo.error = error instanceof Error ? error.message : 'Unknown error';
      throw error;
    }
  }

  private async enforceMemoryLimits(): Promise<void> {
    const loadedModels = Array.from(this.models.values())
      .filter(model => model.status === 'ready')
      .sort((a, b) => a.lastUsed.getTime() - b.lastUsed.getTime());

    // Unload oldest models if we're at the limit
    while (loadedModels.length >= this.maxModels) {
      const oldestModel = loadedModels.shift();
      if (oldestModel) {
        await this.unloadModel(oldestModel.id);
        console.log(`🗑️ Unloaded model to free memory: ${oldestModel.id}`);
      }
    }
  }

  async unloadModel(modelId: string): Promise<void> {
    const model = this.models.get(modelId);
    if (!model || model.status !== 'ready') return;

    try {
      // Clear the pipeline reference
      model.pipeline = null;
      model.status = 'unloaded';
      
      // Force garbage collection if available
      if (global.gc) {
        global.gc();
      }

      console.log(`🗑️ Model unloaded: ${modelId}`);
    } catch (error) {
      console.error(`❌ Error unloading model ${modelId}:`, error);
    }
  }

  async getModel(modelId: string, task: keyof typeof MODEL_CONFIGS): Promise<any> {
    try {
      return await this.loadModel(modelId, task);
    } catch (error) {
      console.error(`❌ Failed to get model ${modelId}:`, error);
      throw new Error(`Model ${modelId} is not available: ${error instanceof Error ? error.message : 'Unknown error'}`);
    }
  }

  getModelStatus(): ModelInfo[] {
    return Array.from(this.models.values()).map(model => ({
      ...model,
      pipeline: undefined // Don't include the actual pipeline in status
    }));
  }

  getLoadedModelCount(): number {
    return Array.from(this.models.values()).filter(model => model.status === 'ready').length;
  }

  async cleanup(): Promise<void> {
    console.log('🧹 Cleaning up AI models...');
    
    const unloadPromises = Array.from(this.models.keys()).map(modelId => 
      this.unloadModel(modelId)
    );
    
    await Promise.allSettled(unloadPromises);
    this.models.clear();
    this.loadingPromises.clear();
    
    console.log('✅ AI models cleanup completed');
  }

  private setupCleanup(): void {
    // Cleanup on process exit
    const cleanup = () => {
      this.cleanup().catch(console.error);
    };

    process.on('exit', cleanup);
    process.on('SIGINT', cleanup);
    process.on('SIGTERM', cleanup);
    process.on('uncaughtException', cleanup);
  }

  // Health check method
  async healthCheck(): Promise<{ healthy: boolean; details: any }> {
    const models = this.getModelStatus();
    const loadedCount = this.getLoadedModelCount();
    const errorCount = models.filter(m => m.status === 'error').length;
    
    return {
      healthy: loadedCount > 0 && errorCount === 0,
      details: {
        totalModels: models.length,
        loadedModels: loadedCount,
        errorModels: errorCount,
        isInitialized: this.isInitialized,
        models: models.map(m => ({
          id: m.id,
          status: m.status,
          modelName: m.modelName,
          lastUsed: m.lastUsed,
          usageCount: m.usageCount
        }))
      }
    };
  }
}

// Singleton instance
export const aiModelManager = new AIModelManager();
