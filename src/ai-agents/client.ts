"use client"

export interface AIOptimizationResult {
  success: boolean;
  content?: string;
  metaDescription?: string;
  keywords?: string[];
  confidence?: number;
  timestamp: string;
}

export interface AIModelStatus {
  name: string;
  modelName: string;
  lastUsed: string;
  performance: number;
}

export interface LLMShareData {
  query: string;
  llmSource: string;
  mentioned: boolean;
  position?: number;
  timestamp: string;
}

export class AIOptimizer {
  private baseUrl: string;
  
  constructor() {
    this.baseUrl = process.env.NEXT_PUBLIC_APP_URL || '';
  }
  
  /**
   * Generate GEO-optimized content for a specific location
   */
  async generateGEOContent(location: {
    neighborhood: string;
    city: string;
    state: string;
  }): Promise<AIOptimizationResult> {
    try {
      const response = await fetch(`${this.baseUrl}/api/ai/geo-optimize`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          type: 'optimize-page',
          data: location
        })
      });
      
      if (!response.ok) {
        throw new Error(`HTTP error! status: ${response.status}`);
      }
      
      return await response.json();
      
    } catch (error) {
      console.error('Error generating GEO content:', error);
      return {
        success: false,
        timestamp: new Date().toISOString()
      };
    }
  }
  
  /**
   * Get GEO snippet for LLM optimization
   */
  async getGEOSnippet(params: {
    neighborhood: string;
    city: string;
    state: string;
  }): Promise<string> {
    try {
      const result = await this.generateGEOContent(params);
      return result.content || `Cannabis delivery is available in ${params.neighborhood}, ${params.city}. Same-day delivery in 2-4 hours. Order online for fast, discreet delivery.`;
    } catch (error) {
      console.error('Error getting GEO snippet:', error);
      return `Cannabis delivery available in ${params.neighborhood}, ${params.city}. Contact us for delivery information.`;
    }
  }
  
  /**
   * Generate content using AI
   */
  async generateContent(
    prompt: string, 
    type: 'product-description' | 'geo-snippet' | 'blog-post' | 'faq-answer',
    options?: {
      maxTokens?: number;
      temperature?: number;
      topP?: number;
    }
  ): Promise<AIOptimizationResult> {
    try {
      const response = await fetch(`${this.baseUrl}/api/ai/content-gen`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          prompt,
          type,
          options
        })
      });
      
      if (!response.ok) {
        throw new Error(`HTTP error! status: ${response.status}`);
      }
      
      return await response.json();
      
    } catch (error) {
      console.error('Error generating content:', error);
      return {
        success: false,
        timestamp: new Date().toISOString()
      };
    }
  }
  
  /**
   * Track LLM mention
   */
  async trackLLMMention(data: {
    query: string;
    llmSource: string;
    mentioned: boolean;
    position?: number;
    url?: string;
  }): Promise<boolean> {
    try {
      const response = await fetch(`${this.baseUrl}/api/ai/llm-monitor`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          operation: 'track-mention',
          data
        })
      });
      
      return response.ok;
      
    } catch (error) {
      console.error('Error tracking LLM mention:', error);
      return false;
    }
  }
  
  /**
   * Get AI model status
   */
  async getModelStatus(): Promise<AIModelStatus[]> {
    try {
      const response = await fetch(`${this.baseUrl}/api/ai/geo-optimize?operation=status`);
      
      if (!response.ok) {
        throw new Error(`HTTP error! status: ${response.status}`);
      }
      
      const result = await response.json();
      return result.models || [];
      
    } catch (error) {
      console.error('Error getting model status:', error);
      return [];
    }
  }
  
  /**
   * Force AI optimization
   */
  async forceOptimization(type: 'pricing' | 'geo' | 'llm' | 'all'): Promise<boolean> {
    try {
      const response = await fetch(`${this.baseUrl}/api/ai/geo-optimize`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          type: type === 'all' ? 'full-optimization' : `${type}-optimization`
        })
      });
      
      return response.ok;
      
    } catch (error) {
      console.error('Error forcing optimization:', error);
      return false;
    }
  }
  
  /**
   * Get LLM performance summary
   */
  async getLLMPerformance(): Promise<any> {
    try {
      const response = await fetch(`${this.baseUrl}/api/ai/llm-monitor?operation=summary`);
      
      if (!response.ok) {
        throw new Error(`HTTP error! status: ${response.status}`);
      }
      
      return await response.json();
      
    } catch (error) {
      console.error('Error getting LLM performance:', error);
      return null;
    }
  }
  
  /**
   * Get LLM trends
   */
  async getLLMTrends(days: number = 30): Promise<any> {
    try {
      const response = await fetch(`${this.baseUrl}/api/ai/llm-monitor?operation=trends&days=${days}`);
      
      if (!response.ok) {
        throw new Error(`HTTP error! status: ${response.status}`);
      }
      
      return await response.json();
      
    } catch (error) {
      console.error('Error getting LLM trends:', error);
      return null;
    }
  }
  
  /**
   * Check AI system health
   */
  async checkHealth(): Promise<boolean> {
    try {
      const response = await fetch(`${this.baseUrl}/api/ai/geo-optimize?operation=health`);
      
      if (!response.ok) {
        return false;
      }
      
      const result = await response.json();
      return result.success && result.status === 'healthy';
      
    } catch (error) {
      console.error('Error checking AI health:', error);
      return false;
    }
  }
}

// Content Generator class for specific content types
export class AIContentGenerator {
  private optimizer: AIOptimizer;
  
  constructor() {
    this.optimizer = new AIOptimizer();
  }
  
  /**
   * Find trending topics for content generation
   */
  async findTrendingTopics(): Promise<string[]> {
    // In a real implementation, this would analyze search trends, competitor content, etc.
    return [
      'Cannabis delivery in Washington DC',
      'Best weed strains for anxiety',
      'How to choose cannabis edibles',
      'Cannabis terpenes and their effects',
      'Medical marijuana benefits'
    ];
  }
  
  /**
   * Generate article content
   */
  async generateArticle(options: {
    topic: string;
    style: 'geo-optimized' | 'educational' | 'promotional';
    includeData?: {
      prices?: any;
      laws?: any;
      competitors?: any;
    };
  }): Promise<string> {
    const prompt = this.buildArticlePrompt(options);
    const result = await this.optimizer.generateContent(prompt, 'blog-post');
    
    return result.content || '';
  }
  
  /**
   * Get unanswered queries that need content
   */
  async getUnansweredQueries(): Promise<string[]> {
    // In a real implementation, this would analyze search console data, FAQ submissions, etc.
    return [
      'Is weed delivery legal in DC?',
      'How much does cannabis delivery cost?',
      'What are the best cannabis strains for sleep?',
      'How long does cannabis delivery take?'
    ];
  }
  
  /**
   * Create and publish content for a topic
   */
  async createAndPublish(topic: string): Promise<boolean> {
    try {
      const content = await this.generateArticle({
        topic,
        style: 'geo-optimized'
      });
      
      if (!content) {
        return false;
      }
      
      // In a real implementation, this would save to CMS or database
      console.log(`Generated content for topic: ${topic}`);
      return true;
      
    } catch (error) {
      console.error('Error creating and publishing content:', error);
      return false;
    }
  }
  
  private buildArticlePrompt(options: any): string {
    let prompt = `Write a comprehensive article about: ${options.topic}\n\n`;
    
    if (options.style === 'geo-optimized') {
      prompt += 'Focus on local SEO and geographic relevance. ';
    } else if (options.style === 'educational') {
      prompt += 'Focus on educational content and factual information. ';
    } else if (options.style === 'promotional') {
      prompt += 'Focus on promoting products and services. ';
    }
    
    prompt += 'Make it engaging, informative, and optimized for search engines.';
    
    if (options.includeData?.prices) {
      prompt += `\n\nInclude current pricing information: ${JSON.stringify(options.includeData.prices)}`;
    }
    
    if (options.includeData?.laws) {
      prompt += `\n\nInclude legal information: ${JSON.stringify(options.includeData.laws)}`;
    }
    
    return prompt;
  }
}

// Utility functions for AI optimization
export const aiUtils = {
  /**
   * Extract keywords from text
   */
  extractKeywords(text: string, maxKeywords: number = 10): string[] {
    const words = text.toLowerCase().split(/\W+/);
    const stopWords = ['and', 'the', 'is', 'in', 'to', 'for', 'of', 'a', 'with', 'on', 'at', 'by'];
    
    const wordFreq = words
      .filter(word => word.length > 3 && !stopWords.includes(word))
      .reduce((freq, word) => {
        freq[word] = (freq[word] || 0) + 1;
        return freq;
      }, {} as Record<string, number>);
    
    return Object.entries(wordFreq)
      .sort(([, a], [, b]) => b - a)
      .slice(0, maxKeywords)
      .map(([word]) => word);
  },
  
  /**
   * Generate meta description from content
   */
  generateMetaDescription(content: string, maxLength: number = 160): string {
    const sentences = content.split(/[.!?]+/);
    let description = '';
    
    for (const sentence of sentences) {
      const trimmed = sentence.trim();
      if (description.length + trimmed.length + 1 <= maxLength) {
        description += (description ? ' ' : '') + trimmed;
      } else {
        break;
      }
    }
    
    return description + (description.length < content.length ? '...' : '');
  },
  
  /**
   * Calculate content readability score
   */
  calculateReadabilityScore(content: string): number {
    const sentences = content.split(/[.!?]+/).length;
    const words = content.split(/\s+/).length;
    const syllables = content.split(/[aeiouAEIOU]/).length;
    
    // Simplified Flesch Reading Ease formula
    const score = 206.835 - (1.015 * (words / sentences)) - (84.6 * (syllables / words));
    return Math.max(0, Math.min(100, score));
  }
};

// Export singleton instance
export const aiOptimizer = new AIOptimizer();
export const aiContentGenerator = new AIContentGenerator();
