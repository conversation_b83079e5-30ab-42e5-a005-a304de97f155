import { <PERSON><PERSON><PERSON><PERSON> } from 'cron';
import { createClient } from '@supabase/supabase-js';
import { aiModelManager } from '../model-manager';

export class GEOWorker {
  private job: CronJob;
  private supabase;
  private isInitialized = false;
  
  constructor() {
    // Initialize Supabase client
    this.supabase = createClient(
      process.env.NEXT_PUBLIC_SUPABASE_URL!,
      process.env.SUPABASE_SERVICE_ROLE_KEY!
    );
    
    // Run every hour
    this.job = new CronJob('0 * * * *', async () => {
      await this.optimizeGEOPages();
    });
  }
  
  async initialize(): Promise<void> {
    if (this.isInitialized) return;

    console.log('🌍 Initializing GEO Worker...');

    try {
      // Initialize the model manager
      await aiModelManager.initialize();

      this.isInitialized = true;
      console.log('✅ GEO Worker initialized successfully');

    } catch (error) {
      console.error('❌ Failed to initialize GEO Worker:', error);
      throw error;
    }
  }
  
  async start(): Promise<void> {
    if (!this.isInitialized) {
      await this.initialize();
    }
    
    this.job.start();
    console.log('🚀 GEO Worker started - optimizing for AI search engines');
    
    // Run initial optimization
    await this.optimizeGEOPages();
  }
  
  async stop(): Promise<void> {
    this.job.stop();
    console.log('⏹️ GEO Worker stopped');
  }
  
  async optimizeGEOPages(): Promise<void> {
    try {
      console.log('🔄 Running GEO page optimization...');
      
      // Get pages that need optimization
      const { data: pages, error } = await this.supabase
        .from('geo_pages')
        .select('*')
        .or('ai_content.is.null,last_ai_update.lt.' + new Date(Date.now() - 24 * 60 * 60 * 1000).toISOString())
        .limit(10);
      
      if (error) {
        throw new Error(`Failed to retrieve pages: ${error.message}`);
      }
      
      if (!pages || pages.length === 0) {
        console.log('ℹ️ No pages need optimization at this time');
        return;
      }
      
      console.log(`🔍 Found ${pages.length} pages to optimize`);
      
      for (const page of pages) {
        // Check LLM visibility
        const visibility = await this.checkLLMVisibility(page);
        
        if (visibility < 0.3 || !page.ai_content) { // Less than 30% LLM share or no content
          // Generate better content
          const optimized = await this.generateOptimizedContent(page);
          
          // Update page
          const { error: updateError } = await this.supabase
            .from('geo_pages')
            .update({ 
              ai_content: optimized.content,
              meta_description: optimized.metaDescription,
              keywords: optimized.keywords,
              last_ai_update: new Date().toISOString(),
              llm_visibility_score: visibility
            })
            .eq('id', page.id);
          
          if (updateError) {
            console.error(`❌ Failed to update page ${page.id}:`, updateError);
            continue;
          }
          
          console.log(`✅ Optimized page: ${page.neighborhood}, ${page.city}`);
          
          // Log optimization
          await this.logOptimization(page.id, visibility, optimized.confidence);
        } else {
          console.log(`ℹ️ Page has good visibility (${Math.round(visibility * 100)}%): ${page.neighborhood}, ${page.city}`);
        }
      }
      
      console.log('✅ GEO optimization cycle completed');
      
    } catch (error) {
      console.error('❌ Error in GEO optimization:', error);
      await this.logError('geo_optimization', error);
    }
  }
  
  private async checkLLMVisibility(page: any): Promise<number> {
    try {
      // In a real implementation, this would check actual LLM responses
      // For now, we'll simulate with random values or use stored data
      
      const { data } = await this.supabase
        .from('llm_tracking')
        .select('*')
        .ilike('query', `%${page.neighborhood}%`)
        .order('timestamp', { ascending: false })
        .limit(5);
      
      if (data && data.length > 0) {
        // Calculate visibility from real data
        const mentionedCount = data.filter(item => item.mentioned).length;
        return mentionedCount / data.length;
      }
      
      // Simulate visibility score if no data
      return Math.random() * 0.5 + 0.2; // Random between 0.2 and 0.7
      
    } catch (error) {
      console.error('Error checking LLM visibility:', error);
      return 0.2; // Default to low visibility on error
    }
  }
  
  private async generateOptimizedContent(page: any): Promise<any> {
    if (!this.model) {
      throw new Error('GEO model not initialized');
    }
    
    const prompt = `Create a comprehensive answer for: "Is weed delivery available in ${page.neighborhood}, ${page.city}?"

Include:
- Delivery availability (Yes, same-day delivery available)
- Delivery times (2-4 hours)
- Price range ($35-65 typical order)
- Legal limits (DC: 2oz gifting, MD: medical only, VA: not yet legal)
- Age requirement (21+)
- Service area coverage
- Ordering process

Style: Authoritative, fact-dense, helpful
Length: 80-120 words
Last updated: ${new Date().toLocaleDateString()}`;

    const result = await this.model(prompt, {
      max_new_tokens: 150,
      temperature: 0.4,
      do_sample: true
    });

    const content = this.cleanGeneratedText(result[0].generated_text);
    
    return {
      content,
      metaDescription: content.substring(0, 160),
      keywords: this.extractKeywords(page),
      confidence: 0.85
    };
  }
  
  private cleanGeneratedText(text: string): string {
    return text.replace(/^.*?:/, '').trim().substring(0, 500);
  }
  
  private extractKeywords(page: any): string[] {
    return [
      `${page.neighborhood} weed delivery`,
      `${page.city} cannabis delivery`,
      `marijuana delivery ${page.neighborhood}`,
      'same day delivery',
      'cannabis dispensary'
    ];
  }
  
  private async logOptimization(pageId: string, oldVisibility: number, confidence: number): Promise<void> {
    await this.supabase
      .from('ai_optimization_logs')
      .insert({
        page_id: pageId,
        optimization_type: 'geo',
        old_visibility_score: oldVisibility,
        confidence_score: confidence,
        completed_at: new Date().toISOString()
      });
  }
  
  private async logError(operation: string, error: any): Promise<void> {
    await this.supabase
      .from('ai_error_logs')
      .insert({
        operation,
        error_message: error.message,
        error_stack: error.stack,
        occurred_at: new Date().toISOString()
      });
  }
  
  // Public methods for manual operations
  async forceOptimize(pageId?: string): Promise<void> {
    if (pageId) {
      // Optimize specific page
      const { data: page, error } = await this.supabase
        .from('geo_pages')
        .select('*')
        .eq('id', pageId)
        .single();
      
      if (error || !page) {
        throw new Error(`Page not found: ${error?.message}`);
      }
      
      const visibility = await this.checkLLMVisibility(page);
      const optimized = await this.generateOptimizedContent(page);
      
      await this.supabase
        .from('geo_pages')
        .update({ 
          ai_content: optimized.content,
          meta_description: optimized.metaDescription,
          keywords: optimized.keywords,
          last_ai_update: new Date().toISOString(),
          llm_visibility_score: visibility
        })
        .eq('id', pageId);
      
      await this.logOptimization(pageId, visibility, optimized.confidence);
      
    } else {
      // Optimize all pages
      await this.optimizeGEOPages();
    }
  }
  
  async getStatus(): Promise<any> {
    return {
      isInitialized: this.isInitialized,
      isRunning: this.job.running,
      modelLoaded: this.model !== null,
      nextRun: this.job.running ? this.job.nextDate().toJSDate() : null
    };
  }
}
