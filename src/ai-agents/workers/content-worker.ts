import { <PERSON><PERSON><PERSON><PERSON> } from 'cron';
import { createClient } from '@supabase/supabase-js';
import { pipeline } from '@xenova/transformers';

export class ContentWorker {
  private job: CronJob;
  private supabase;
  private model: any = null;
  private isInitialized = false;
  
  constructor() {
    // Initialize Supabase client
    this.supabase = createClient(
      process.env.NEXT_PUBLIC_SUPABASE_URL!,
      process.env.SUPABASE_SERVICE_ROLE_KEY!
    );
    
    // Run every 4 hours
    this.job = new CronJob('0 */4 * * *', async () => {
      await this.generateContent();
    });
  }
  
  async initialize(): Promise<void> {
    if (this.isInitialized) return;
    
    console.log('📝 Initializing Content Worker...');
    
    try {
      // Load lightweight model for content generation
      this.model = await pipeline('text-generation', 'Xenova/LaMini-Flan-T5-248M', {
        quantized: true
      });
      
      this.isInitialized = true;
      console.log('✅ Content Worker initialized successfully');
      
    } catch (error) {
      console.error('❌ Failed to initialize Content Worker:', error);
      throw error;
    }
  }
  
  async start(): Promise<void> {
    if (!this.isInitialized) {
      await this.initialize();
    }
    
    this.job.start();
    console.log('🚀 Content Worker started - generating AI-optimized content');
  }
  
  async stop(): Promise<void> {
    this.job.stop();
    console.log('⏹️ Content Worker stopped');
  }
  
  async generateContent(): Promise<void> {
    try {
      console.log('🔄 Running content generation cycle...');
      
      // Generate different types of content
      await Promise.all([
        this.generateProductDescriptions(),
        this.generateBlogPosts(),
        this.generateFAQAnswers()
      ]);
      
      console.log('✅ Content generation cycle completed');
      
    } catch (error) {
      console.error('❌ Error in content generation:', error);
      await this.logError('content_generation', error);
    }
  }
  
  private async generateProductDescriptions(): Promise<void> {
    try {
      // Get products that need descriptions
      const { data: products, error } = await this.supabase
        .from('products')
        .select('*')
        .or('description.is.null,ai_generated.is.false')
        .limit(5);
      
      if (error) {
        throw new Error(`Failed to retrieve products: ${error.message}`);
      }
      
      if (!products || products.length === 0) {
        console.log('ℹ️ No products need descriptions at this time');
        return;
      }
      
      console.log(`🔍 Found ${products.length} products needing descriptions`);
      
      for (const product of products) {
        const description = await this.generateProductDescription(product);
        
        // Update product
        const { error: updateError } = await this.supabase
          .from('products')
          .update({ 
            description: description.content,
            meta_description: description.metaDescription,
            ai_generated: true,
            last_ai_update: new Date().toISOString()
          })
          .eq('id', product.id);
        
        if (updateError) {
          console.error(`❌ Failed to update product ${product.id}:`, updateError);
          continue;
        }
        
        console.log(`✅ Generated description for product: ${product.name}`);
      }
      
    } catch (error) {
      console.error('❌ Error generating product descriptions:', error);
      throw error;
    }
  }
  
  private async generateBlogPosts(): Promise<void> {
    try {
      // Check if we need to generate new blog posts
      const { count, error } = await this.supabase
        .from('blog_posts')
        .select('*', { count: 'exact', head: true })
        .gte('created_at', new Date(Date.now() - 7 * 24 * 60 * 60 * 1000).toISOString());
      
      if (error) {
        throw new Error(`Failed to check blog posts: ${error.message}`);
      }
      
      // Only generate new posts if we haven't created any in the past week
      if (count && count >= 2) {
        console.log('ℹ️ Sufficient recent blog posts exist');
        return;
      }
      
      // Get trending topics
      const topics = await this.getTrendingTopics();
      
      for (const topic of topics.slice(0, 2)) { // Generate up to 2 posts
        const post = await this.generateBlogPost(topic);
        
        // Insert new blog post
        const { error: insertError } = await this.supabase
          .from('blog_posts')
          .insert({
            title: post.title,
            content: post.content,
            excerpt: post.excerpt,
            slug: this.generateSlug(post.title),
            keywords: post.keywords,
            ai_generated: true,
            created_at: new Date().toISOString(),
            published: true
          });
        
        if (insertError) {
          console.error(`❌ Failed to insert blog post:`, insertError);
          continue;
        }
        
        console.log(`✅ Generated blog post: ${post.title}`);
      }
      
    } catch (error) {
      console.error('❌ Error generating blog posts:', error);
      throw error;
    }
  }
  
  private async generateFAQAnswers(): Promise<void> {
    try {
      // Get unanswered FAQs
      const { data: faqs, error } = await this.supabase
        .from('faqs')
        .select('*')
        .is('answer', null)
        .limit(10);
      
      if (error) {
        throw new Error(`Failed to retrieve FAQs: ${error.message}`);
      }
      
      if (!faqs || faqs.length === 0) {
        console.log('ℹ️ No unanswered FAQs at this time');
        return;
      }
      
      console.log(`🔍 Found ${faqs.length} unanswered FAQs`);
      
      for (const faq of faqs) {
        const answer = await this.generateFAQAnswer(faq.question);
        
        // Update FAQ
        const { error: updateError } = await this.supabase
          .from('faqs')
          .update({ 
            answer: answer.content,
            ai_generated: true,
            last_updated: new Date().toISOString()
          })
          .eq('id', faq.id);
        
        if (updateError) {
          console.error(`❌ Failed to update FAQ ${faq.id}:`, updateError);
          continue;
        }
        
        console.log(`✅ Generated answer for FAQ: ${faq.question}`);
      }
      
    } catch (error) {
      console.error('❌ Error generating FAQ answers:', error);
      throw error;
    }
  }
  
  private async generateProductDescription(product: any): Promise<any> {
    if (!this.model) {
      throw new Error('Content model not initialized');
    }
    
    const prompt = `Write a compelling cannabis product description for: ${product.name}
Category: ${product.category}
Strain: ${product.strain || 'N/A'}
THC: ${product.thc_content || 'N/A'}
CBD: ${product.cbd_content || 'N/A'}

Include details about effects, flavor profile, potency, and ideal usage scenarios.
Keep it professional, informative, and appealing to cannabis consumers.
Length: 100-150 words`;

    const result = await this.model(prompt, {
      max_new_tokens: 200,
      temperature: 0.7,
      do_sample: true
    });

    const content = this.cleanGeneratedText(result[0].generated_text);
    
    return {
      content,
      metaDescription: content.substring(0, 160)
    };
  }
  
  private async generateBlogPost(topic: string): Promise<any> {
    if (!this.model) {
      throw new Error('Content model not initialized');
    }
    
    // Generate title
    const titlePrompt = `Create an SEO-friendly blog post title about: ${topic}
Make it engaging and click-worthy while being informative.`;

    const titleResult = await this.model(titlePrompt, {
      max_new_tokens: 50,
      temperature: 0.8
    });

    const title = this.cleanGeneratedText(titleResult[0].generated_text).replace(/^Title: /, '');
    
    // Generate content
    const contentPrompt = `Write an informative blog post about: ${topic}
Title: ${title}

Include relevant facts, benefits, and educational content about cannabis.
Make it engaging, authoritative, and SEO-friendly.
Structure with introduction, main points, and conclusion.
Length: 300-400 words`;

    const contentResult = await this.model(contentPrompt, {
      max_new_tokens: 500,
      temperature: 0.7
    });

    const content = this.cleanGeneratedText(contentResult[0].generated_text);
    
    return {
      title,
      content,
      excerpt: content.substring(0, 150) + '...',
      keywords: this.extractKeywords(topic, title)
    };
  }
  
  private async generateFAQAnswer(question: string): Promise<any> {
    if (!this.model) {
      throw new Error('Content model not initialized');
    }
    
    const prompt = `Answer this cannabis-related question: ${question}

Provide a clear, factual, and helpful response.
Include relevant details and keep it conversational.
Ensure accuracy and compliance with cannabis regulations.
Length: 50-100 words`;

    const result = await this.model(prompt, {
      max_new_tokens: 150,
      temperature: 0.5
    });

    const content = this.cleanGeneratedText(result[0].generated_text);
    
    return {
      content,
      keywords: this.extractKeywordsFromQuestion(question)
    };
  }
  
  private async getTrendingTopics(): Promise<string[]> {
    // In a real implementation, this would analyze search trends, competitor content, etc.
    // For now, we'll return some predefined topics
    return [
      'Benefits of different cannabis consumption methods',
      'Understanding cannabis terpenes and their effects',
      'How to choose the right cannabis strain for anxiety',
      'The entourage effect: How cannabinoids work together',
      'Cannabis and sleep: Finding the right product for insomnia'
    ];
  }
  
  private cleanGeneratedText(text: string): string {
    return text.replace(/^.*?:/, '').trim();
  }
  
  private generateSlug(title: string): string {
    return title
      .toLowerCase()
      .replace(/[^\w\s]/g, '')
      .replace(/\s+/g, '-');
  }
  
  private extractKeywords(topic: string, title: string): string[] {
    // Simple keyword extraction
    const combined = `${topic} ${title}`;
    const words = combined.toLowerCase().split(/\W+/);
    const stopWords = ['and', 'the', 'is', 'in', 'to', 'for', 'of', 'a', 'with'];
    
    return [...new Set(words)]
      .filter(word => word.length > 3 && !stopWords.includes(word))
      .slice(0, 5);
  }
  
  private extractKeywordsFromQuestion(question: string): string[] {
    const words = question.toLowerCase().split(/\W+/);
    const stopWords = ['what', 'how', 'why', 'when', 'where', 'is', 'are', 'can', 'do', 'does'];
    
    return [...new Set(words)]
      .filter(word => word.length > 3 && !stopWords.includes(word))
      .slice(0, 5);
  }
  
  private async logError(operation: string, error: any): Promise<void> {
    await this.supabase
      .from('ai_error_logs')
      .insert({
        operation,
        error_message: error.message,
        error_stack: error.stack,
        occurred_at: new Date().toISOString()
      });
  }
  
  // Public methods for manual operations
  async generateSpecificContent(type: 'product' | 'blog' | 'faq', data: any): Promise<any> {
    switch (type) {
      case 'product':
        return this.generateProductDescription(data);
      case 'blog':
        return this.generateBlogPost(data.topic);
      case 'faq':
        return this.generateFAQAnswer(data.question);
      default:
        throw new Error(`Invalid content type: ${type}`);
    }
  }
  
  async getStatus(): Promise<any> {
    return {
      isInitialized: this.isInitialized,
      isRunning: this.job.running,
      modelLoaded: this.model !== null,
      nextRun: this.job.running ? this.job.nextDate().toJSDate() : null
    };
  }
}
