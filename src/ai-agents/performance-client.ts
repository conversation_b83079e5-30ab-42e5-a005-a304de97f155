"use client"

/**
 * Performance-optimized AI client for WeedNearMeDC
 * Designed for fast loading, minimal bundle size, and deployment flexibility
 * Compatible with WordPress, Vercel, Netlify, and other platforms
 */

interface CacheEntry {
  data: any;
  timestamp: number;
  ttl: number; // Time to live in milliseconds
}

interface AIResponse {
  success: boolean;
  content?: string;
  cached?: boolean;
  responseTime?: number;
  error?: string;
}

export class PerformanceAIClient {
  private cache = new Map<string, CacheEntry>();
  private baseUrl: string;
  private maxCacheSize = 100; // Limit cache size for memory efficiency
  private defaultTTL = 5 * 60 * 1000; // 5 minutes default cache
  
  constructor(baseUrl?: string) {
    // Auto-detect base URL for different deployment environments
    this.baseUrl = baseUrl || this.detectBaseUrl();
    this.setupCacheCleanup();
  }
  
  private detectBaseUrl(): string {
    if (typeof window !== 'undefined') {
      // Client-side detection
      return window.location.origin;
    }
    
    // Server-side detection for various environments
    return process.env.NEXT_PUBLIC_APP_URL || 
           process.env.VERCEL_URL ? `https://${process.env.VERCEL_URL}` :
           process.env.WORDPRESS_URL ||
           'http://localhost:3000';
  }
  
  /**
   * Generate GEO content with aggressive caching for performance
   */
  async generateGEOContent(location: {
    neighborhood: string;
    city: string;
    state: string;
  }): Promise<AIResponse> {
    const startTime = Date.now();
    const cacheKey = `geo:${location.neighborhood}:${location.city}:${location.state}`;
    
    // Check cache first for instant response
    const cached = this.getFromCache(cacheKey);
    if (cached) {
      return {
        success: true,
        content: cached,
        cached: true,
        responseTime: Date.now() - startTime
      };
    }
    
    try {
      // Use fetch with timeout for reliability
      const controller = new AbortController();
      const timeoutId = setTimeout(() => controller.abort(), 10000); // 10s timeout
      
      const response = await fetch(`${this.baseUrl}/api/ai/geo-optimize`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          type: 'optimize-page',
          data: location
        }),
        signal: controller.signal
      });
      
      clearTimeout(timeoutId);
      
      if (!response.ok) {
        throw new Error(`HTTP ${response.status}: ${response.statusText}`);
      }
      
      const result = await response.json();
      
      if (result.success && result.content) {
        // Cache successful results for 30 minutes
        this.setCache(cacheKey, result.content.snippet || result.content, 30 * 60 * 1000);
        
        return {
          success: true,
          content: result.content.snippet || result.content,
          cached: false,
          responseTime: Date.now() - startTime
        };
      }
      
      throw new Error(result.error || 'AI generation failed');
      
    } catch (error) {
      console.error('AI generation error:', error);
      
      // Return fallback content for reliability
      const fallbackContent = this.getFallbackGEOContent(location);
      
      return {
        success: false,
        content: fallbackContent,
        cached: false,
        responseTime: Date.now() - startTime,
        error: error instanceof Error ? error.message : 'Unknown error'
      };
    }
  }
  
  /**
   * Get cached GEO snippet instantly (for WordPress integration)
   */
  getCachedGEOSnippet(location: {
    neighborhood: string;
    city: string;
    state: string;
  }): string | null {
    const cacheKey = `geo:${location.neighborhood}:${location.city}:${location.state}`;
    return this.getFromCache(cacheKey);
  }
  
  /**
   * Preload content for better performance
   */
  async preloadGEOContent(locations: Array<{
    neighborhood: string;
    city: string;
    state: string;
  }>): Promise<void> {
    const promises = locations.map(location => 
      this.generateGEOContent(location).catch(error => {
        console.warn(`Failed to preload ${location.neighborhood}:`, error);
        return null;
      })
    );
    
    await Promise.allSettled(promises);
    console.log(`✅ Preloaded content for ${locations.length} locations`);
  }
  
  /**
   * Health check for monitoring
   */
  async healthCheck(): Promise<{ healthy: boolean; responseTime: number; details: any }> {
    const startTime = Date.now();
    
    try {
      const response = await fetch(`${this.baseUrl}/api/ai/geo-optimize?operation=health`, {
        method: 'GET',
        headers: { 'Content-Type': 'application/json' }
      });
      
      const result = await response.json();
      
      return {
        healthy: response.ok && result.success,
        responseTime: Date.now() - startTime,
        details: result
      };
      
    } catch (error) {
      return {
        healthy: false,
        responseTime: Date.now() - startTime,
        details: { error: error instanceof Error ? error.message : 'Unknown error' }
      };
    }
  }
  
  // Cache management methods
  private getFromCache(key: string): any | null {
    const entry = this.cache.get(key);
    if (!entry) return null;
    
    if (Date.now() > entry.timestamp + entry.ttl) {
      this.cache.delete(key);
      return null;
    }
    
    return entry.data;
  }
  
  private setCache(key: string, data: any, ttl: number = this.defaultTTL): void {
    // Implement LRU cache behavior
    if (this.cache.size >= this.maxCacheSize) {
      const firstKey = this.cache.keys().next().value;
      this.cache.delete(firstKey);
    }
    
    this.cache.set(key, {
      data,
      timestamp: Date.now(),
      ttl
    });
  }
  
  private setupCacheCleanup(): void {
    // Clean expired entries every 5 minutes
    setInterval(() => {
      const now = Date.now();
      for (const [key, entry] of this.cache.entries()) {
        if (now > entry.timestamp + entry.ttl) {
          this.cache.delete(key);
        }
      }
    }, 5 * 60 * 1000);
  }
  
  private getFallbackGEOContent(location: {
    neighborhood: string;
    city: string;
    state: string;
  }): string {
    const stateInfo = {
      DC: { legal: 'medical with self-certification', delivery: '2-4 hours', minOrder: '$50' },
      MD: { legal: 'recreational and medical', delivery: '3-5 hours', minOrder: '$75' },
      VA: { legal: 'medical only', delivery: 'next day', minOrder: '$100' }
    };
    
    const info = stateInfo[location.state as keyof typeof stateInfo] || stateInfo.DC;
    
    return `Cannabis delivery is available in ${location.neighborhood}, ${location.city}. ` +
           `We offer ${info.delivery} delivery for ${info.legal} customers. ` +
           `Minimum order ${info.minOrder}. Must be 21+ with valid ID. Order online for fast, discreet delivery.`;
  }
  
  /**
   * Clear cache (useful for WordPress admin)
   */
  clearCache(): void {
    this.cache.clear();
    console.log('🧹 AI cache cleared');
  }
  
  /**
   * Get cache statistics
   */
  getCacheStats(): { size: number; hitRate: number; entries: string[] } {
    return {
      size: this.cache.size,
      hitRate: 0, // Would need to track hits/misses for real implementation
      entries: Array.from(this.cache.keys())
    };
  }
}

// Singleton instance for performance
export const performanceAIClient = new PerformanceAIClient();

// WordPress integration helpers
export const wordpressHelpers = {
  /**
   * Generate shortcode for WordPress
   */
  generateShortcode(location: { neighborhood: string; city: string; state: string }): string {
    return `[weed_delivery_info neighborhood="${location.neighborhood}" city="${location.city}" state="${location.state}"]`;
  },
  
  /**
   * Get content for WordPress REST API
   */
  async getContentForWordPress(location: { neighborhood: string; city: string; state: string }) {
    const result = await performanceAIClient.generateGEOContent(location);
    
    return {
      content: result.content,
      meta: {
        cached: result.cached,
        responseTime: result.responseTime,
        success: result.success
      }
    };
  }
};
