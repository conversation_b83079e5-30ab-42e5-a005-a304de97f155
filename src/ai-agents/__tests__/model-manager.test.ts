import { AIModelManager, MODEL_CONFIGS } from '../model-manager'
import { deploymentAdapter } from '../deployment-adapter'

// Mock the deployment adapter
jest.mock('../deployment-adapter', () => ({
  deploymentAdapter: {
    getAIModelConfig: jest.fn(() => ({
      batchSize: 1,
      maxMemory: '512MB',
      preferredModels: ['distilgpt2']
    })),
    getMetrics: jest.fn(() => ({
      platform: 'test',
      config: { maxConcurrentRequests: 5 }
    })),
    getCacheImplementation: jest.fn(() => new Map())
  }
}))

describe('AIModelManager', () => {
  let modelManager: AIModelManager

  beforeEach(() => {
    modelManager = new AIModelManager()
    jest.clearAllMocks()
  })

  afterEach(async () => {
    await modelManager.cleanup()
  })

  describe('initialization', () => {
    it('should initialize successfully', async () => {
      await expect(modelManager.initialize()).resolves.not.toThrow()
    })

    it('should not initialize twice', async () => {
      await modelManager.initialize()
      await modelManager.initialize() // Should not throw or cause issues
      expect(true).toBe(true) // Test passes if no errors
    })

    it('should configure max models based on platform', () => {
      const status = modelManager.getModelStatus()
      expect(Array.isArray(status)).toBe(true)
    })
  })

  describe('model loading', () => {
    beforeEach(async () => {
      await modelManager.initialize()
    })

    it('should load a text generation model', async () => {
      const model = await modelManager.loadModel('test-model', 'text-generation')
      expect(model).toBeDefined()
      expect(typeof model).toBe('function')
    })

    it('should cache loaded models', async () => {
      const model1 = await modelManager.loadModel('test-model', 'text-generation')
      const model2 = await modelManager.loadModel('test-model', 'text-generation')
      
      expect(model1).toBe(model2) // Should return the same cached instance
    })

    it('should handle model loading errors gracefully', async () => {
      // Mock pipeline to throw an error
      const { pipeline } = require('@xenova/transformers')
      pipeline.mockRejectedValueOnce(new Error('Model loading failed'))

      await expect(
        modelManager.loadModel('failing-model', 'text-generation')
      ).rejects.toThrow('Model loading failed')
    })

    it('should enforce memory limits', async () => {
      // Load multiple models to test memory management
      await modelManager.loadModel('model1', 'text-generation')
      await modelManager.loadModel('model2', 'text-classification')
      await modelManager.loadModel('model3', 'feature-extraction')
      
      const status = modelManager.getModelStatus()
      const loadedModels = status.filter(m => m.status === 'ready')
      
      // Should respect max models limit
      expect(loadedModels.length).toBeLessThanOrEqual(3)
    })
  })

  describe('model management', () => {
    beforeEach(async () => {
      await modelManager.initialize()
    })

    it('should get model status', () => {
      const status = modelManager.getModelStatus()
      expect(Array.isArray(status)).toBe(true)
    })

    it('should count loaded models', () => {
      const count = modelManager.getLoadedModelCount()
      expect(typeof count).toBe('number')
      expect(count).toBeGreaterThanOrEqual(0)
    })

    it('should unload models', async () => {
      await modelManager.loadModel('test-model', 'text-generation')
      await modelManager.unloadModel('test-model')
      
      const status = modelManager.getModelStatus()
      const testModel = status.find(m => m.id === 'test-model')
      
      if (testModel) {
        expect(testModel.status).toBe('unloaded')
      }
    })

    it('should perform health check', async () => {
      const health = await modelManager.healthCheck()
      
      expect(health).toHaveProperty('healthy')
      expect(health).toHaveProperty('details')
      expect(typeof health.healthy).toBe('boolean')
      expect(health.details).toHaveProperty('totalModels')
      expect(health.details).toHaveProperty('loadedModels')
      expect(health.details).toHaveProperty('isInitialized')
    })
  })

  describe('error handling', () => {
    it('should handle uninitialized state', async () => {
      const uninitializedManager = new AIModelManager()
      
      // Should still work but may have different behavior
      const status = uninitializedManager.getModelStatus()
      expect(Array.isArray(status)).toBe(true)
    })

    it('should handle invalid model types', async () => {
      await modelManager.initialize()
      
      await expect(
        modelManager.loadModel('test', 'invalid-task' as any)
      ).rejects.toThrow()
    })
  })

  describe('cleanup', () => {
    it('should cleanup all models', async () => {
      await modelManager.initialize()
      await modelManager.loadModel('test1', 'text-generation')
      await modelManager.loadModel('test2', 'text-classification')
      
      await modelManager.cleanup()
      
      const status = modelManager.getModelStatus()
      expect(status.length).toBe(0)
    })
  })

  describe('configuration', () => {
    it('should use correct model configurations', () => {
      expect(MODEL_CONFIGS).toHaveProperty('text-generation')
      expect(MODEL_CONFIGS).toHaveProperty('text-classification')
      expect(MODEL_CONFIGS).toHaveProperty('feature-extraction')
      
      expect(MODEL_CONFIGS['text-generation']).toHaveProperty('primary')
      expect(MODEL_CONFIGS['text-generation']).toHaveProperty('fallback')
      expect(MODEL_CONFIGS['text-generation']).toHaveProperty('options')
    })

    it('should adapt to deployment environment', () => {
      expect(deploymentAdapter.getAIModelConfig).toHaveBeenCalled()
      expect(deploymentAdapter.getMetrics).toHaveBeenCalled()
    })
  })

  describe('performance', () => {
    beforeEach(async () => {
      await modelManager.initialize()
    })

    it('should track model usage', async () => {
      const model = await modelManager.loadModel('usage-test', 'text-generation')
      
      // Use the model
      await model('test prompt')
      
      const status = modelManager.getModelStatus()
      const usageModel = status.find(m => m.id === 'usage-test')
      
      if (usageModel) {
        expect(usageModel.usageCount).toBeGreaterThan(0)
        expect(usageModel.lastUsed).toBeInstanceOf(Date)
      }
    })

    it('should handle concurrent model loading', async () => {
      const promises = [
        modelManager.loadModel('concurrent1', 'text-generation'),
        modelManager.loadModel('concurrent2', 'text-classification'),
        modelManager.loadModel('concurrent3', 'feature-extraction')
      ]
      
      const models = await Promise.all(promises)
      
      expect(models).toHaveLength(3)
      models.forEach(model => {
        expect(model).toBeDefined()
        expect(typeof model).toBe('function')
      })
    })
  })
})
