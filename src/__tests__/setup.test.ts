/**
 * Basic setup test to verify Jest configuration
 */

describe('Test Setup', () => {
  it('should run basic tests', () => {
    expect(true).toBe(true)
  })

  it('should handle async operations', async () => {
    const result = await Promise.resolve('test')
    expect(result).toBe('test')
  })

  it('should mock console methods', () => {
    console.log('This should be mocked')
    expect(console.log).toHaveBeenCalled()
  })

  it('should have access to DOM APIs', () => {
    const element = document.createElement('div')
    element.textContent = 'Test'
    expect(element.textContent).toBe('Test')
  })

  it('should mock localStorage', () => {
    localStorage.setItem('test', 'value')
    expect(localStorage.setItem).toHaveBeenCalledWith('test', 'value')

    // Test that we can get the value
    localStorage.getItem.mockReturnValue('value')
    expect(localStorage.getItem('test')).toBe('value')
  })

  it('should mock fetch', async () => {
    const mockResponse = { ok: true, json: () => Promise.resolve({ data: 'test' }) }
    global.fetch.mockResolvedValueOnce(mockResponse)

    const response = await fetch('/api/test')
    expect(response.ok).toBe(true)
    expect(fetch).toHaveBeenCalledWith('/api/test')
  })
})
