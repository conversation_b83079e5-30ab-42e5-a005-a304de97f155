import { Metadata } from 'next'
import { generateMetadata, generateLocalBusinessSchema } from '@/lib/seo/metadata'

interface LocationData {
  city: string
  state: string
  zipCode: string
  county?: string
  population?: number
  coordinates?: {
    lat: number
    lng: number
  }
}

interface LocationSEOProps {
  location: LocationData
  serviceType?: 'delivery' | 'pickup' | 'both'
}

export function generateLocationMetadata({ location, serviceType = 'delivery' }: LocationSEOProps): Metadata {
  const { city, state, zipCode, county } = location
  const cityFormatted = city.replace(/-/g, ' ').replace(/\b\w/g, l => l.toUpperCase())
  const stateFormatted = state.toUpperCase()
  
  const title = `Cannabis Delivery ${cityFormatted}, ${stateFormatted} | WeedNearMeDC`
  const description = `Premium cannabis delivery in ${cityFormatted}, ${stateFormatted}. Same-day delivery, lab-tested products, discreet service. Order online for fast delivery in ${zipCode}.`
  
  const keywords = [
    `cannabis delivery ${cityFormatted}`,
    `weed delivery ${cityFormatted}`,
    `marijuana delivery ${cityFormatted}`,
    `cannabis ${cityFormatted} ${stateFormatted}`,
    `dispensary delivery ${cityFormatted}`,
    `THC delivery ${cityFormatted}`,
    `CBD delivery ${cityFormatted}`,
    `cannabis delivery ${zipCode}`,
    `same day cannabis delivery ${cityFormatted}`,
    `legal cannabis ${cityFormatted}`,
    ...(county ? [`cannabis delivery ${county} county`] : [])
  ]

  const structuredData = generateLocalBusinessSchema({
    name: `WeedNearMeDC ${cityFormatted}`,
    address: `${cityFormatted}, ${stateFormatted}`,
    city: cityFormatted,
    state: stateFormatted,
    zipCode,
    hours: [
      'Mo-Su 09:00-21:00'
    ]
  })

  return generateMetadata({
    title,
    description,
    keywords,
    canonical: `https://weednearmedc.com/locations/${city}`,
    structuredData
  })
}

// Generate FAQ schema for location pages
export function generateLocationFAQSchema(location: LocationData) {
  const { city, state } = location
  const cityFormatted = city.replace(/-/g, ' ').replace(/\b\w/g, l => l.toUpperCase())
  const stateFormatted = state.toUpperCase()

  return {
    '@context': 'https://schema.org',
    '@type': 'FAQPage',
    mainEntity: [
      {
        '@type': 'Question',
        name: `Do you deliver cannabis to ${cityFormatted}, ${stateFormatted}?`,
        acceptedAnswer: {
          '@type': 'Answer',
          text: `Yes, we provide premium cannabis delivery service to ${cityFormatted}, ${stateFormatted}. We offer same-day delivery with discreet, professional service.`
        }
      },
      {
        '@type': 'Question',
        name: `What are your delivery hours in ${cityFormatted}?`,
        acceptedAnswer: {
          '@type': 'Answer',
          text: `We deliver to ${cityFormatted} Monday through Sunday from 9:00 AM to 9:00 PM. Orders placed before 7:00 PM typically qualify for same-day delivery.`
        }
      },
      {
        '@type': 'Question',
        name: `Is cannabis delivery legal in ${cityFormatted}, ${stateFormatted}?`,
        acceptedAnswer: {
          '@type': 'Answer',
          text: `Yes, cannabis delivery is legal in ${cityFormatted}, ${stateFormatted} for adults 21 and older. We operate in full compliance with local and state regulations.`
        }
      },
      {
        '@type': 'Question',
        name: `What products do you deliver to ${cityFormatted}?`,
        acceptedAnswer: {
          '@type': 'Answer',
          text: `We deliver a full range of cannabis products to ${cityFormatted} including flower, edibles, concentrates, vapes, topicals, and accessories. All products are lab-tested for quality and safety.`
        }
      },
      {
        '@type': 'Question',
        name: `How much is delivery to ${cityFormatted}?`,
        acceptedAnswer: {
          '@type': 'Answer',
          text: `Delivery to ${cityFormatted} is free for orders over $50. For orders under $50, there is a small delivery fee. Check our website for current promotions and deals.`
        }
      }
    ]
  }
}

// Generate breadcrumb schema for location pages
export function generateLocationBreadcrumbSchema(location: LocationData) {
  const { city, state } = location
  const cityFormatted = city.replace(/-/g, ' ').replace(/\b\w/g, l => l.toUpperCase())

  return {
    '@context': 'https://schema.org',
    '@type': 'BreadcrumbList',
    itemListElement: [
      {
        '@type': 'ListItem',
        position: 1,
        name: 'Home',
        item: 'https://weednearmedc.com'
      },
      {
        '@type': 'ListItem',
        position: 2,
        name: 'Delivery Locations',
        item: 'https://weednearmedc.com/locations'
      },
      {
        '@type': 'ListItem',
        position: 3,
        name: `${cityFormatted}, ${state.toUpperCase()}`,
        item: `https://weednearmedc.com/locations/${city}`
      }
    ]
  }
}

// Location-specific content generator
export function generateLocationContent(location: LocationData) {
  const { city, state, zipCode, county, population } = location
  const cityFormatted = city.replace(/-/g, ' ').replace(/\b\w/g, l => l.toUpperCase())
  const stateFormatted = state.toUpperCase()

  return {
    heroTitle: `Premium Cannabis Delivery in ${cityFormatted}, ${stateFormatted}`,
    heroSubtitle: `Fast, discreet, and reliable cannabis delivery to ${cityFormatted}. Same-day delivery available.`,
    
    aboutSection: {
      title: `Cannabis Delivery Service in ${cityFormatted}`,
      content: `WeedNearMeDC is proud to serve the ${cityFormatted} community with premium cannabis delivery services. Our ${cityFormatted} customers enjoy access to the finest selection of lab-tested cannabis products, delivered directly to their door with discretion and professionalism.`
    },
    
    serviceAreas: {
      title: `Delivery Areas Near ${cityFormatted}`,
      content: `We deliver throughout ${cityFormatted} and surrounding areas in ${county ? `${county} County` : stateFormatted}. Our delivery zone includes all neighborhoods within ${cityFormatted} and nearby communities.`
    },
    
    products: {
      title: `Cannabis Products Available in ${cityFormatted}`,
      content: `Our ${cityFormatted} delivery menu features a comprehensive selection of premium cannabis products including top-shelf flower, artisanal edibles, premium concentrates, and quality accessories.`
    },
    
    legal: {
      title: `Legal Cannabis Delivery in ${cityFormatted}`,
      content: `Cannabis delivery is legal in ${cityFormatted}, ${stateFormatted} for adults 21 and older. We operate in full compliance with all local and state regulations, ensuring a safe and legal cannabis experience for our ${cityFormatted} customers.`
    },
    
    stats: population ? {
      title: `Serving ${population.toLocaleString()} Residents in ${cityFormatted}`,
      content: `With a population of ${population.toLocaleString()}, ${cityFormatted} is an important part of our delivery network. We're committed to providing exceptional cannabis delivery service to every resident of ${cityFormatted}.`
    } : undefined
  }
}

// SEO-optimized location page component
export function LocationSEOContent({ location }: { location: LocationData }) {
  const content = generateLocationContent(location)
  const faqSchema = generateLocationFAQSchema(location)
  const breadcrumbSchema = generateLocationBreadcrumbSchema(location)

  return (
    <>
      {/* Structured Data */}
      <script
        type="application/ld+json"
        dangerouslySetInnerHTML={{
          __html: JSON.stringify(faqSchema)
        }}
      />
      <script
        type="application/ld+json"
        dangerouslySetInnerHTML={{
          __html: JSON.stringify(breadcrumbSchema)
        }}
      />

      {/* SEO-optimized content sections */}
      <div className="space-y-8">
        <section>
          <h1 className="text-3xl font-bold mb-4">{content.heroTitle}</h1>
          <p className="text-lg text-gray-600">{content.heroSubtitle}</p>
        </section>

        <section>
          <h2 className="text-2xl font-semibold mb-3">{content.aboutSection.title}</h2>
          <p className="text-gray-700">{content.aboutSection.content}</p>
        </section>

        <section>
          <h2 className="text-2xl font-semibold mb-3">{content.serviceAreas.title}</h2>
          <p className="text-gray-700">{content.serviceAreas.content}</p>
        </section>

        <section>
          <h2 className="text-2xl font-semibold mb-3">{content.products.title}</h2>
          <p className="text-gray-700">{content.products.content}</p>
        </section>

        <section>
          <h2 className="text-2xl font-semibold mb-3">{content.legal.title}</h2>
          <p className="text-gray-700">{content.legal.content}</p>
        </section>

        {content.stats && (
          <section>
            <h2 className="text-2xl font-semibold mb-3">{content.stats.title}</h2>
            <p className="text-gray-700">{content.stats.content}</p>
          </section>
        )}
      </div>
    </>
  )
}
