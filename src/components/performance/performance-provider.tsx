"use client"

import React, { createContext, useContext, useEffect, useState } from 'react'
import { getPerformanceMonitor, usePerformanceMonitoring } from '@/lib/performance/monitoring'

interface PerformanceContextType {
  isMonitoring: boolean
  metrics: any
  startMonitoring: () => void
  stopMonitoring: () => void
  sendMetrics: () => void
}

const PerformanceContext = createContext<PerformanceContextType | undefined>(undefined)

interface PerformanceProviderProps {
  children: React.ReactNode
  enableMonitoring?: boolean
  autoSend?: boolean
  sendInterval?: number
}

export function PerformanceProvider({
  children,
  enableMonitoring = true,
  autoSend = true,
  sendInterval = 30000 // 30 seconds
}: PerformanceProviderProps) {
  const [isMonitoring, setIsMonitoring] = useState(enableMonitoring)
  const [metrics, setMetrics] = useState({})
  
  const monitor = getPerformanceMonitor()
  
  // Use the performance monitoring hook
  const { getMetrics, sendMetrics } = usePerformanceMonitoring()

  useEffect(() => {
    if (!isMonitoring) return

    // Update metrics periodically
    const updateMetrics = () => {
      setMetrics(getMetrics())
    }

    const metricsInterval = setInterval(updateMetrics, 1000)

    // Auto-send metrics if enabled
    let sendInterval: NodeJS.Timeout | undefined
    if (autoSend) {
      sendInterval = setInterval(() => {
        sendMetrics()
      }, sendInterval)
    }

    return () => {
      clearInterval(metricsInterval)
      if (sendInterval) {
        clearInterval(sendInterval)
      }
    }
  }, [isMonitoring, autoSend, sendInterval, getMetrics, sendMetrics])

  const startMonitoring = () => {
    setIsMonitoring(true)
  }

  const stopMonitoring = () => {
    setIsMonitoring(false)
  }

  const handleSendMetrics = () => {
    sendMetrics()
  }

  const value: PerformanceContextType = {
    isMonitoring,
    metrics,
    startMonitoring,
    stopMonitoring,
    sendMetrics: handleSendMetrics
  }

  return (
    <PerformanceContext.Provider value={value}>
      {children}
    </PerformanceContext.Provider>
  )
}

export function usePerformance() {
  const context = useContext(PerformanceContext)
  if (context === undefined) {
    throw new Error('usePerformance must be used within a PerformanceProvider')
  }
  return context
}

// Performance debugging component for development
export function PerformanceDebugger() {
  const { metrics, isMonitoring } = usePerformance()
  const [isVisible, setIsVisible] = useState(false)

  // Only show in development
  if (process.env.NODE_ENV !== 'development') {
    return null
  }

  if (!isMonitoring) {
    return null
  }

  return (
    <>
      {/* Toggle button */}
      <button
        onClick={() => setIsVisible(!isVisible)}
        className="fixed bottom-4 right-4 z-50 bg-blue-600 text-white p-2 rounded-full shadow-lg hover:bg-blue-700 transition-colors"
        title="Toggle Performance Metrics"
      >
        📊
      </button>

      {/* Metrics panel */}
      {isVisible && (
        <div className="fixed bottom-16 right-4 z-50 bg-white border border-gray-300 rounded-lg shadow-lg p-4 max-w-sm">
          <h3 className="font-bold text-sm mb-2">Performance Metrics</h3>
          <div className="space-y-1 text-xs">
            {Object.entries(metrics).map(([key, value]) => (
              <div key={key} className="flex justify-between">
                <span className="text-gray-600">{key}:</span>
                <span className="font-mono">
                  {typeof value === 'number' ? value.toFixed(2) : String(value)}
                </span>
              </div>
            ))}
          </div>
        </div>
      )}
    </>
  )
}

// Performance warning component
export function PerformanceWarning() {
  const { metrics } = usePerformance()
  const [warnings, setWarnings] = useState<string[]>([])

  useEffect(() => {
    const newWarnings: string[] = []

    // Check for performance issues
    if (metrics.largestContentfulPaint > 2500) {
      newWarnings.push('Slow LCP detected')
    }
    if (metrics.firstInputDelay > 100) {
      newWarnings.push('High input delay')
    }
    if (metrics.cumulativeLayoutShift > 0.1) {
      newWarnings.push('Layout shift detected')
    }

    setWarnings(newWarnings)
  }, [metrics])

  // Only show in development
  if (process.env.NODE_ENV !== 'development' || warnings.length === 0) {
    return null
  }

  return (
    <div className="fixed top-4 right-4 z-50 bg-yellow-100 border border-yellow-400 text-yellow-800 px-4 py-2 rounded-lg shadow-lg">
      <h4 className="font-bold text-sm">Performance Warnings:</h4>
      <ul className="text-xs mt-1">
        {warnings.map((warning, index) => (
          <li key={index}>• {warning}</li>
        ))}
      </ul>
    </div>
  )
}

// Resource hints component for preloading critical resources
export function ResourceHints() {
  useEffect(() => {
    // Preload critical fonts
    const fontLinks = [
      '/fonts/inter-var.woff2',
      // Add other critical fonts
    ]

    fontLinks.forEach(href => {
      const link = document.createElement('link')
      link.rel = 'preload'
      link.as = 'font'
      link.type = 'font/woff2'
      link.crossOrigin = 'anonymous'
      link.href = href
      document.head.appendChild(link)
    })

    // Preload critical images
    const criticalImages = [
      '/images/hero-bg.webp',
      '/images/logo.webp',
      // Add other critical images
    ]

    criticalImages.forEach(src => {
      const link = document.createElement('link')
      link.rel = 'preload'
      link.as = 'image'
      link.href = src
      document.head.appendChild(link)
    })

    // DNS prefetch for external domains
    const externalDomains = [
      'https://fonts.googleapis.com',
      'https://fonts.gstatic.com',
      // Add other external domains
    ]

    externalDomains.forEach(href => {
      const link = document.createElement('link')
      link.rel = 'dns-prefetch'
      link.href = href
      document.head.appendChild(link)
    })

  }, [])

  return null
}
