import React from 'react'
import { render, screen, waitFor, act } from '@testing-library/react'
import userEvent from '@testing-library/user-event'
import { AuthProvider, useAuth } from '../auth-provider'
import { supabase } from '@/lib/supabase/client'

// Mock Supabase
jest.mock('@/lib/supabase/client')

// Test component that uses the auth context
const TestComponent = () => {
  const { user, isLoading, login, register, logout, verifyAge, uploadIdDocument } = useAuth()
  
  return (
    <div>
      <div data-testid="loading">{isLoading ? 'Loading' : 'Not Loading'}</div>
      <div data-testid="user">{user ? user.email : 'No User'}</div>
      <button onClick={() => login('<EMAIL>', 'password')}>Login</button>
      <button onClick={() => register({
        email: '<EMAIL>',
        password: 'password',
        firstName: 'Test',
        lastName: 'User',
        phone: '**********',
        dateOfBirth: '1990-01-01'
      })}>Register</button>
      <button onClick={logout}>Logout</button>
      <button onClick={() => verifyAge('1990-01-01')}>Verify Age</button>
      <button onClick={() => uploadIdDocument(new File(['test'], 'id.jpg', { type: 'image/jpeg' }))}>
        Upload ID
      </button>
    </div>
  )
}

const renderWithAuth = () => {
  return render(
    <AuthProvider>
      <TestComponent />
    </AuthProvider>
  )
}

describe('AuthProvider', () => {
  const mockSupabase = supabase as jest.Mocked<typeof supabase>
  
  beforeEach(() => {
    jest.clearAllMocks()
    
    // Setup default mocks
    mockSupabase.auth.getSession.mockResolvedValue({
      data: { session: null },
      error: null
    })
    
    mockSupabase.auth.onAuthStateChange.mockReturnValue({
      data: { subscription: { unsubscribe: jest.fn() } }
    })
  })

  describe('initialization', () => {
    it('should render without crashing', () => {
      renderWithAuth()
      expect(screen.getByTestId('loading')).toBeInTheDocument()
    })

    it('should start in loading state', () => {
      renderWithAuth()
      expect(screen.getByTestId('loading')).toHaveTextContent('Loading')
    })

    it('should check for existing session on mount', async () => {
      renderWithAuth()
      
      await waitFor(() => {
        expect(mockSupabase.auth.getSession).toHaveBeenCalled()
      })
    })

    it('should set up auth state change listener', () => {
      renderWithAuth()
      expect(mockSupabase.auth.onAuthStateChange).toHaveBeenCalled()
    })
  })

  describe('login', () => {
    it('should call Supabase signInWithPassword', async () => {
      const user = userEvent.setup()
      
      mockSupabase.auth.signInWithPassword.mockResolvedValue({
        data: { user: { id: '1', email: '<EMAIL>' }, session: {} },
        error: null
      })
      
      renderWithAuth()
      
      await user.click(screen.getByText('Login'))
      
      await waitFor(() => {
        expect(mockSupabase.auth.signInWithPassword).toHaveBeenCalledWith({
          email: '<EMAIL>',
          password: 'password'
        })
      })
    })

    it('should handle login errors', async () => {
      const user = userEvent.setup()
      
      mockSupabase.auth.signInWithPassword.mockResolvedValue({
        data: { user: null, session: null },
        error: { message: 'Invalid credentials' }
      })
      
      renderWithAuth()
      
      await expect(async () => {
        await user.click(screen.getByText('Login'))
      }).rejects.toThrow('Invalid credentials')
    })
  })

  describe('registration', () => {
    it('should call Supabase signUp with user data', async () => {
      const user = userEvent.setup()
      
      mockSupabase.auth.signUp.mockResolvedValue({
        data: { user: { id: '1', email: '<EMAIL>' }, session: {} },
        error: null
      })
      
      renderWithAuth()
      
      await user.click(screen.getByText('Register'))
      
      await waitFor(() => {
        expect(mockSupabase.auth.signUp).toHaveBeenCalledWith({
          email: '<EMAIL>',
          password: 'password',
          options: {
            data: {
              firstName: 'Test',
              lastName: 'User',
              phone: '**********',
              dateOfBirth: '1990-01-01'
            }
          }
        })
      })
    })

    it('should validate age during registration', async () => {
      const user = userEvent.setup()
      
      // Mock register function to use underage date
      const TestComponentUnderage = () => {
        const { register } = useAuth()
        
        return (
          <button onClick={() => register({
            email: '<EMAIL>',
            password: 'password',
            firstName: 'Test',
            lastName: 'User',
            phone: '**********',
            dateOfBirth: '2010-01-01' // Underage
          })}>Register Underage</button>
        )
      }
      
      render(
        <AuthProvider>
          <TestComponentUnderage />
        </AuthProvider>
      )
      
      await expect(async () => {
        await user.click(screen.getByText('Register Underage'))
      }).rejects.toThrow('You must be 21 or older to register')
    })

    it('should handle registration errors', async () => {
      const user = userEvent.setup()
      
      mockSupabase.auth.signUp.mockResolvedValue({
        data: { user: null, session: null },
        error: { message: 'Email already exists' }
      })
      
      renderWithAuth()
      
      await expect(async () => {
        await user.click(screen.getByText('Register'))
      }).rejects.toThrow('Email already exists')
    })
  })

  describe('logout', () => {
    it('should call Supabase signOut', async () => {
      const user = userEvent.setup()
      
      mockSupabase.auth.signOut.mockResolvedValue({ error: null })
      
      renderWithAuth()
      
      await user.click(screen.getByText('Logout'))
      
      await waitFor(() => {
        expect(mockSupabase.auth.signOut).toHaveBeenCalled()
      })
    })

    it('should handle logout errors', async () => {
      const user = userEvent.setup()
      
      mockSupabase.auth.signOut.mockResolvedValue({
        error: { message: 'Logout failed' }
      })
      
      renderWithAuth()
      
      await expect(async () => {
        await user.click(screen.getByText('Logout'))
      }).rejects.toThrow('Logout failed')
    })
  })

  describe('age verification', () => {
    it('should verify age for users 21 and older', async () => {
      const user = userEvent.setup()
      
      // Mock authenticated user
      const mockUser = {
        id: '1',
        email: '<EMAIL>',
        firstName: 'Test',
        lastName: 'User',
        ageVerified: false,
        idVerified: false,
        role: 'CUSTOMER' as const,
        createdAt: new Date(),
        updatedAt: new Date()
      }
      
      // Mock the auth context to have a user
      const TestComponentWithUser = () => {
        const { verifyAge } = useAuth()
        
        React.useEffect(() => {
          // Simulate having a user
        }, [])
        
        return (
          <button onClick={() => verifyAge('1990-01-01')}>Verify Age</button>
        )
      }
      
      mockSupabase.from.mockReturnValue({
        update: jest.fn().mockReturnValue({
          eq: jest.fn().mockResolvedValue({ error: null })
        })
      } as any)
      
      render(
        <AuthProvider>
          <TestComponentWithUser />
        </AuthProvider>
      )
      
      await user.click(screen.getByText('Verify Age'))
      
      // Should not throw for valid age
      await waitFor(() => {
        expect(true).toBe(true) // Test passes if no error
      })
    })

    it('should reject age verification for users under 21', async () => {
      const user = userEvent.setup()
      
      renderWithAuth()
      
      // Mock verifyAge with underage date
      const TestComponentUnderage = () => {
        const { verifyAge } = useAuth()
        
        return (
          <button onClick={() => verifyAge('2010-01-01')}>Verify Underage</button>
        )
      }
      
      render(
        <AuthProvider>
          <TestComponentUnderage />
        </AuthProvider>
      )
      
      await expect(async () => {
        await user.click(screen.getByText('Verify Underage'))
      }).rejects.toThrow('You must be 21 or older to use this service')
    })
  })

  describe('ID document upload', () => {
    it('should upload ID document to Supabase storage', async () => {
      const user = userEvent.setup()
      
      mockSupabase.storage.from.mockReturnValue({
        upload: jest.fn().mockResolvedValue({ error: null })
      } as any)
      
      mockSupabase.from.mockReturnValue({
        update: jest.fn().mockReturnValue({
          eq: jest.fn().mockResolvedValue({ error: null })
        })
      } as any)
      
      renderWithAuth()
      
      await user.click(screen.getByText('Upload ID'))
      
      await waitFor(() => {
        expect(mockSupabase.storage.from).toHaveBeenCalledWith('id-documents')
      })
    })

    it('should handle upload errors', async () => {
      const user = userEvent.setup()
      
      mockSupabase.storage.from.mockReturnValue({
        upload: jest.fn().mockResolvedValue({ error: { message: 'Upload failed' } })
      } as any)
      
      renderWithAuth()
      
      await expect(async () => {
        await user.click(screen.getByText('Upload ID'))
      }).rejects.toThrow('Upload failed')
    })
  })

  describe('user profile management', () => {
    it('should load existing user profile', async () => {
      const mockUser = {
        id: '1',
        email: '<EMAIL>',
        firstName: 'Test',
        lastName: 'User',
        ageVerified: true,
        idVerified: false,
        role: 'CUSTOMER',
        createdAt: new Date().toISOString(),
        updatedAt: new Date().toISOString()
      }
      
      mockSupabase.from.mockReturnValue({
        select: jest.fn().mockReturnValue({
          eq: jest.fn().mockReturnValue({
            single: jest.fn().mockResolvedValue({ data: mockUser, error: null })
          })
        })
      } as any)
      
      mockSupabase.auth.getSession.mockResolvedValue({
        data: { 
          session: { 
            user: { 
              id: '1', 
              email: '<EMAIL>',
              user_metadata: {}
            } 
          } 
        },
        error: null
      })
      
      renderWithAuth()
      
      await waitFor(() => {
        expect(mockSupabase.from).toHaveBeenCalledWith('users')
      })
    })

    it('should create new user profile if none exists', async () => {
      mockSupabase.from.mockReturnValue({
        select: jest.fn().mockReturnValue({
          eq: jest.fn().mockReturnValue({
            single: jest.fn().mockResolvedValue({ 
              data: null, 
              error: { code: 'PGRST116' } // Not found error
            })
          })
        }),
        insert: jest.fn().mockReturnValue({
          select: jest.fn().mockReturnValue({
            single: jest.fn().mockResolvedValue({ 
              data: {
                id: '1',
                email: '<EMAIL>',
                firstName: '',
                lastName: '',
                ageVerified: false,
                idVerified: false,
                role: 'CUSTOMER',
                createdAt: new Date().toISOString(),
                updatedAt: new Date().toISOString()
              }, 
              error: null 
            })
          })
        })
      } as any)
      
      mockSupabase.auth.getSession.mockResolvedValue({
        data: { 
          session: { 
            user: { 
              id: '1', 
              email: '<EMAIL>',
              user_metadata: {}
            } 
          } 
        },
        error: null
      })
      
      renderWithAuth()
      
      await waitFor(() => {
        expect(mockSupabase.from).toHaveBeenCalledWith('users')
      })
    })
  })
})
