"use client"

import { createContext, useContext, useEffect, useState } from 'react'
import { User } from '@/types'
import { supabase } from '@/lib/supabase/client'
import type { User as SupabaseUser, Session } from '@supabase/supabase-js'

interface AuthContextType {
  user: User | null
  session: Session | null
  isLoading: boolean
  login: (email: string, password: string) => Promise<void>
  register: (userData: RegisterData) => Promise<void>
  logout: () => Promise<void>
  updateProfile: (data: Partial<User>) => Promise<void>
  verifyAge: (dateOfBirth: string) => Promise<void>
  uploadIdDocument: (file: File) => Promise<void>
}

interface RegisterData {
  email: string
  password: string
  firstName: string
  lastName: string
  phone: string
  dateOfBirth: string
}

const AuthContext = createContext<AuthContextType | undefined>(undefined)

export function AuthProvider({ children }: { children: React.ReactNode }) {
  const [user, setUser] = useState<User | null>(null)
  const [session, setSession] = useState<Session | null>(null)
  const [isLoading, setIsLoading] = useState(true)

  useEffect(() => {
    // Get initial session
    getInitialSession()

    // Listen for auth changes
    const { data: { subscription } } = supabase.auth.onAuthStateChange(
      async (event, session) => {
        console.log('Auth state changed:', event, session?.user?.email)
        setSession(session)

        if (session?.user) {
          await loadUserProfile(session.user)
        } else {
          setUser(null)
        }

        setIsLoading(false)
      }
    )

    return () => subscription.unsubscribe()
  }, [])

  const getInitialSession = async () => {
    try {
      const { data: { session }, error } = await supabase.auth.getSession()

      if (error) {
        console.error('Error getting session:', error)
        return
      }

      setSession(session)

      if (session?.user) {
        await loadUserProfile(session.user)
      }
    } catch (error) {
      console.error('Error checking session:', error)
    } finally {
      setIsLoading(false)
    }
  }

  const loadUserProfile = async (supabaseUser: SupabaseUser) => {
    try {
      // First check if user profile exists in our users table
      const { data: existingUser, error: fetchError } = await supabase
        .from('users')
        .select('*')
        .eq('id', supabaseUser.id)
        .single()

      if (fetchError && fetchError.code !== 'PGRST116') {
        console.error('Error fetching user profile:', fetchError)
        return
      }

      if (existingUser) {
        // User exists, use the profile data
        setUser({
          id: existingUser.id,
          email: existingUser.email,
          firstName: existingUser.firstName,
          lastName: existingUser.lastName,
          phone: existingUser.phone,
          dateOfBirth: existingUser.dateOfBirth ? new Date(existingUser.dateOfBirth) : undefined,
          ageVerified: existingUser.ageVerified,
          idVerified: existingUser.idVerified,
          role: existingUser.role,
          createdAt: new Date(existingUser.createdAt),
          updatedAt: new Date(existingUser.updatedAt)
        })
      } else {
        // User doesn't exist in our users table, create profile
        const newUserData = {
          id: supabaseUser.id,
          email: supabaseUser.email!,
          firstName: supabaseUser.user_metadata?.firstName || '',
          lastName: supabaseUser.user_metadata?.lastName || '',
          phone: supabaseUser.user_metadata?.phone || '',
          ageVerified: false,
          idVerified: false,
          role: 'CUSTOMER' as const,
          createdAt: new Date().toISOString(),
          updatedAt: new Date().toISOString()
        }

        const { data: createdUser, error: createError } = await supabase
          .from('users')
          .insert(newUserData)
          .select()
          .single()

        if (createError) {
          console.error('Error creating user profile:', createError)
          return
        }

        setUser({
          id: createdUser.id,
          email: createdUser.email,
          firstName: createdUser.firstName,
          lastName: createdUser.lastName,
          phone: createdUser.phone,
          dateOfBirth: createdUser.dateOfBirth ? new Date(createdUser.dateOfBirth) : undefined,
          ageVerified: createdUser.ageVerified,
          idVerified: createdUser.idVerified,
          role: createdUser.role,
          createdAt: new Date(createdUser.createdAt),
          updatedAt: new Date(createdUser.updatedAt)
        })
      }
    } catch (error) {
      console.error('Error loading user profile:', error)
    }
  }

  const login = async (email: string, password: string) => {
    setIsLoading(true)
    try {
      const { data, error } = await supabase.auth.signInWithPassword({
        email,
        password
      })

      if (error) {
        throw new Error(error.message)
      }

      // User profile will be loaded automatically via the auth state change listener
      console.log('Login successful for:', email)

    } catch (error) {
      setIsLoading(false)
      throw error
    }
  }

  const register = async (userData: RegisterData) => {
    setIsLoading(true)
    try {
      // Check age verification first
      const birthDate = new Date(userData.dateOfBirth)
      const age = new Date().getFullYear() - birthDate.getFullYear()

      if (age < 21) {
        throw new Error('You must be 21 or older to register')
      }

      const { data, error } = await supabase.auth.signUp({
        email: userData.email,
        password: userData.password,
        options: {
          data: {
            firstName: userData.firstName,
            lastName: userData.lastName,
            phone: userData.phone,
            dateOfBirth: userData.dateOfBirth
          }
        }
      })

      if (error) {
        throw new Error(error.message)
      }

      // If email confirmation is required, inform the user
      if (data.user && !data.session) {
        throw new Error('Please check your email and click the confirmation link to complete registration')
      }

      console.log('Registration successful for:', userData.email)

    } catch (error) {
      setIsLoading(false)
      throw error
    }
  }

  const logout = async () => {
    try {
      const { error } = await supabase.auth.signOut()

      if (error) {
        console.error('Error logging out:', error)
        throw new Error(error.message)
      }

      // Clear local state (will also be cleared by auth state change listener)
      setUser(null)
      setSession(null)

      console.log('Logout successful')
    } catch (error) {
      console.error('Error logging out:', error)
      throw error
    }
  }

  const updateProfile = async (data: Partial<User>) => {
    if (!user || !session) {
      throw new Error('User not authenticated')
    }

    try {
      // Update user profile in our users table
      const { error: updateError } = await supabase
        .from('users')
        .update({
          ...data,
          updatedAt: new Date().toISOString()
        })
        .eq('id', user.id)

      if (updateError) {
        throw new Error(updateError.message)
      }

      // Update local state
      const updatedUser = {
        ...user,
        ...data,
        updatedAt: new Date()
      }
      setUser(updatedUser)

      console.log('Profile updated successfully')
    } catch (error) {
      console.error('Error updating profile:', error)
      throw error
    }
  }

  const verifyAge = async (dateOfBirth: string) => {
    if (!user) {
      throw new Error('User not authenticated')
    }

    try {
      const birthDate = new Date(dateOfBirth)
      const age = new Date().getFullYear() - birthDate.getFullYear()

      if (age < 21) {
        throw new Error('You must be 21 or older to use this service')
      }

      // Update user with age verification
      await updateProfile({
        dateOfBirth: birthDate,
        ageVerified: true
      })

      console.log('Age verification successful')
    } catch (error) {
      console.error('Error verifying age:', error)
      throw error
    }
  }

  const uploadIdDocument = async (file: File) => {
    if (!user || !session) {
      throw new Error('User not authenticated')
    }

    try {
      // Upload ID document to Supabase storage
      const fileExt = file.name.split('.').pop()
      const fileName = `${user.id}/id-document.${fileExt}`

      const { error: uploadError } = await supabase.storage
        .from('id-documents')
        .upload(fileName, file, {
          upsert: true
        })

      if (uploadError) {
        throw new Error(uploadError.message)
      }

      // Update user profile to indicate ID document uploaded
      await updateProfile({
        idVerified: false // Will be verified manually by admin
      })

      console.log('ID document uploaded successfully')
    } catch (error) {
      console.error('Error uploading ID document:', error)
      throw error
    }
  }

  const value = {
    user,
    session,
    isLoading,
    login,
    register,
    logout,
    updateProfile,
    verifyAge,
    uploadIdDocument
  }

  return (
    <AuthContext.Provider value={value}>
      {children}
    </AuthContext.Provider>
  )
}

export function useAuth() {
  const context = useContext(AuthContext)
  if (context === undefined) {
    throw new Error('useAuth must be used within an AuthProvider')
  }
  return context
}
