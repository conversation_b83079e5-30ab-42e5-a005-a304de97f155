"use client"

import { useState } from "react"
import { But<PERSON> } from "@/components/ui/button"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { Alert, AlertDescription } from "@/components/ui/alert"
import { Calendar, Shield, AlertTriangle, CheckCircle } from "lucide-react"
import { useAuth } from "@/components/providers/auth-provider"

interface AgeVerificationProps {
  onVerified: () => void
  onSkip?: () => void
}

export default function AgeVerification({ onVerified, onSkip }: AgeVerificationProps) {
  const { verifyAge } = useAuth()
  const [dateOfBirth, setDateOfBirth] = useState("")
  const [isLoading, setIsLoading] = useState(false)
  const [error, setError] = useState("")
  const [step, setStep] = useState<'warning' | 'verify' | 'success'>('warning')

  const handleContinue = () => {
    setStep('verify')
  }

  const handleVerifyAge = async (e: React.FormEvent) => {
    e.preventDefault()
    setError("")
    setIsLoading(true)

    try {
      if (!dateOfBirth) {
        throw new Error("Please enter your date of birth")
      }

      const birthDate = new Date(dateOfBirth)
      const today = new Date()
      const age = today.getFullYear() - birthDate.getFullYear()
      const monthDiff = today.getMonth() - birthDate.getMonth()
      
      // More precise age calculation
      const actualAge = monthDiff < 0 || (monthDiff === 0 && today.getDate() < birthDate.getDate()) 
        ? age - 1 
        : age

      if (actualAge < 21) {
        throw new Error("You must be 21 years or older to access this site")
      }

      await verifyAge(dateOfBirth)
      setStep('success')
      
      // Auto-proceed after success message
      setTimeout(() => {
        onVerified()
      }, 2000)

    } catch (error) {
      setError(error instanceof Error ? error.message : "Verification failed")
    } finally {
      setIsLoading(false)
    }
  }

  const handleExit = () => {
    window.location.href = "https://www.samhsa.gov/find-help/national-helpline"
  }

  if (step === 'warning') {
    return (
      <div className="min-h-screen bg-gradient-to-br from-green-50 to-emerald-100 flex items-center justify-center p-4">
        <Card className="w-full max-w-md">
          <CardHeader className="text-center">
            <div className="mx-auto w-16 h-16 bg-amber-100 rounded-full flex items-center justify-center mb-4">
              <AlertTriangle className="w-8 h-8 text-amber-600" />
            </div>
            <CardTitle className="text-2xl font-bold">Age Verification Required</CardTitle>
            <CardDescription className="text-base">
              This website contains information about cannabis products
            </CardDescription>
          </CardHeader>
          <CardContent className="space-y-6">
            <Alert>
              <Shield className="h-4 w-4" />
              <AlertDescription>
                <strong>Legal Notice:</strong> You must be 21 years or older to access this website. 
                Cannabis products are for adults only and may be harmful to your health.
              </AlertDescription>
            </Alert>

            <div className="space-y-3 text-sm text-gray-600">
              <p>• Cannabis products have not been evaluated by the FDA</p>
              <p>• Keep out of reach of children and pets</p>
              <p>• Do not operate vehicles or machinery after use</p>
              <p>• Consult your physician before use if pregnant or nursing</p>
            </div>

            <div className="flex flex-col gap-3">
              <Button 
                onClick={handleContinue}
                className="w-full bg-green-600 hover:bg-green-700"
              >
                I am 21 or older - Continue
              </Button>
              
              <Button 
                variant="outline" 
                onClick={handleExit}
                className="w-full"
              >
                I am under 21 - Exit
              </Button>
              
              {onSkip && (
                <Button 
                  variant="ghost" 
                  onClick={onSkip}
                  className="w-full text-xs"
                >
                  Skip for now (Demo mode)
                </Button>
              )}
            </div>
          </CardContent>
        </Card>
      </div>
    )
  }

  if (step === 'verify') {
    return (
      <div className="min-h-screen bg-gradient-to-br from-green-50 to-emerald-100 flex items-center justify-center p-4">
        <Card className="w-full max-w-md">
          <CardHeader className="text-center">
            <div className="mx-auto w-16 h-16 bg-blue-100 rounded-full flex items-center justify-center mb-4">
              <Calendar className="w-8 h-8 text-blue-600" />
            </div>
            <CardTitle className="text-2xl font-bold">Verify Your Age</CardTitle>
            <CardDescription>
              Please enter your date of birth to confirm you are 21 or older
            </CardDescription>
          </CardHeader>
          <CardContent>
            <form onSubmit={handleVerifyAge} className="space-y-6">
              <div className="space-y-2">
                <Label htmlFor="dateOfBirth">Date of Birth</Label>
                <Input
                  id="dateOfBirth"
                  type="date"
                  value={dateOfBirth}
                  onChange={(e) => setDateOfBirth(e.target.value)}
                  max={new Date().toISOString().split('T')[0]}
                  required
                  className="text-center"
                />
              </div>

              {error && (
                <Alert variant="destructive">
                  <AlertTriangle className="h-4 w-4" />
                  <AlertDescription>{error}</AlertDescription>
                </Alert>
              )}

              <div className="flex flex-col gap-3">
                <Button 
                  type="submit" 
                  disabled={isLoading || !dateOfBirth}
                  className="w-full bg-green-600 hover:bg-green-700"
                >
                  {isLoading ? "Verifying..." : "Verify Age"}
                </Button>
                
                <Button 
                  type="button"
                  variant="outline" 
                  onClick={() => setStep('warning')}
                  className="w-full"
                >
                  Back
                </Button>
              </div>
            </form>

            <div className="mt-6 p-4 bg-gray-50 rounded-lg">
              <p className="text-xs text-gray-600 text-center">
                <Shield className="w-3 h-3 inline mr-1" />
                Your information is secure and used only for age verification
              </p>
            </div>
          </CardContent>
        </Card>
      </div>
    )
  }

  if (step === 'success') {
    return (
      <div className="min-h-screen bg-gradient-to-br from-green-50 to-emerald-100 flex items-center justify-center p-4">
        <Card className="w-full max-w-md">
          <CardHeader className="text-center">
            <div className="mx-auto w-16 h-16 bg-green-100 rounded-full flex items-center justify-center mb-4">
              <CheckCircle className="w-8 h-8 text-green-600" />
            </div>
            <CardTitle className="text-2xl font-bold text-green-600">Age Verified!</CardTitle>
            <CardDescription>
              Welcome to WeedNearMeDC. Redirecting you now...
            </CardDescription>
          </CardHeader>
          <CardContent>
            <div className="flex justify-center">
              <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-green-600"></div>
            </div>
          </CardContent>
        </Card>
      </div>
    )
  }

  return null
}

// Age verification hook for components
export function useAgeVerification() {
  const { user } = useAuth()
  
  const isAgeVerified = user?.ageVerified || false
  const needsVerification = user && !user.ageVerified
  
  return {
    isAgeVerified,
    needsVerification,
    user
  }
}

// Higher-order component for age-protected routes
export function withAgeVerification<P extends object>(
  Component: React.ComponentType<P>
) {
  return function AgeProtectedComponent(props: P) {
    const { isAgeVerified, needsVerification } = useAgeVerification()
    const [showVerification, setShowVerification] = useState(false)

    if (needsVerification && !showVerification) {
      return (
        <AgeVerification
          onVerified={() => setShowVerification(false)}
          onSkip={() => setShowVerification(false)}
        />
      )
    }

    return <Component {...props} />
  }
}
