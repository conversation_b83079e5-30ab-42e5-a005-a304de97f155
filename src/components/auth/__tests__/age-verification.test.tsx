import React from 'react'
import { render, screen, waitFor } from '@testing-library/react'
import userEvent from '@testing-library/user-event'
import AgeVerification, { useAgeVerification, withAgeVerification } from '../age-verification'
import { useAuth } from '@/components/providers/auth-provider'

// Mock the auth provider
jest.mock('@/components/providers/auth-provider', () => ({
  useAuth: jest.fn()
}))

const mockUseAuth = useAuth as jest.MockedFunction<typeof useAuth>

describe('AgeVerification', () => {
  const mockOnVerified = jest.fn()
  const mockOnSkip = jest.fn()
  const mockVerifyAge = jest.fn()

  beforeEach(() => {
    jest.clearAllMocks()
    
    mockUseAuth.mockReturnValue({
      user: null,
      session: null,
      isLoading: false,
      login: jest.fn(),
      register: jest.fn(),
      logout: jest.fn(),
      updateProfile: jest.fn(),
      verifyAge: mockVerifyAge,
      uploadIdDocument: jest.fn()
    })
  })

  describe('warning step', () => {
    it('should render warning step by default', () => {
      render(<AgeVerification onVerified={mockOnVerified} />)
      
      expect(screen.getByText('Age Verification Required')).toBeInTheDocument()
      expect(screen.getByText('I am 21 or older - Continue')).toBeInTheDocument()
      expect(screen.getByText('I am under 21 - Exit')).toBeInTheDocument()
    })

    it('should show skip button when onSkip is provided', () => {
      render(<AgeVerification onVerified={mockOnVerified} onSkip={mockOnSkip} />)
      
      expect(screen.getByText('Skip for now (Demo mode)')).toBeInTheDocument()
    })

    it('should proceed to verification step when continue is clicked', async () => {
      const user = userEvent.setup()
      
      render(<AgeVerification onVerified={mockOnVerified} />)
      
      await user.click(screen.getByText('I am 21 or older - Continue'))
      
      await waitFor(() => {
        expect(screen.getByText('Verify Your Age')).toBeInTheDocument()
      })
    })

    it('should call onSkip when skip button is clicked', async () => {
      const user = userEvent.setup()
      
      render(<AgeVerification onVerified={mockOnVerified} onSkip={mockOnSkip} />)
      
      await user.click(screen.getByText('Skip for now (Demo mode)'))
      
      expect(mockOnSkip).toHaveBeenCalled()
    })

    it('should redirect to help when under 21 is clicked', async () => {
      const user = userEvent.setup()
      
      // Mock window.location
      delete (window as any).location
      window.location = { href: '' } as any
      
      render(<AgeVerification onVerified={mockOnVerified} />)
      
      await user.click(screen.getByText('I am under 21 - Exit'))
      
      expect(window.location.href).toBe('https://www.samhsa.gov/find-help/national-helpline')
    })
  })

  describe('verification step', () => {
    beforeEach(async () => {
      const user = userEvent.setup()
      
      render(<AgeVerification onVerified={mockOnVerified} />)
      
      await user.click(screen.getByText('I am 21 or older - Continue'))
      
      await waitFor(() => {
        expect(screen.getByText('Verify Your Age')).toBeInTheDocument()
      })
    })

    it('should render date input', () => {
      expect(screen.getByLabelText('Date of Birth')).toBeInTheDocument()
    })

    it('should require date of birth', async () => {
      const user = userEvent.setup()
      
      await user.click(screen.getByText('Verify Age'))
      
      expect(screen.getByText('Please enter your date of birth')).toBeInTheDocument()
    })

    it('should verify age for users 21 and older', async () => {
      const user = userEvent.setup()
      
      mockVerifyAge.mockResolvedValue(undefined)
      
      const dateInput = screen.getByLabelText('Date of Birth')
      await user.type(dateInput, '1990-01-01')
      
      await user.click(screen.getByText('Verify Age'))
      
      await waitFor(() => {
        expect(mockVerifyAge).toHaveBeenCalledWith('1990-01-01')
      })
    })

    it('should reject users under 21', async () => {
      const user = userEvent.setup()
      
      const dateInput = screen.getByLabelText('Date of Birth')
      await user.type(dateInput, '2010-01-01') // Underage
      
      await user.click(screen.getByText('Verify Age'))
      
      await waitFor(() => {
        expect(screen.getByText('You must be 21 years or older to access this site')).toBeInTheDocument()
      })
    })

    it('should handle verification errors', async () => {
      const user = userEvent.setup()
      
      mockVerifyAge.mockRejectedValue(new Error('Verification failed'))
      
      const dateInput = screen.getByLabelText('Date of Birth')
      await user.type(dateInput, '1990-01-01')
      
      await user.click(screen.getByText('Verify Age'))
      
      await waitFor(() => {
        expect(screen.getByText('Verification failed')).toBeInTheDocument()
      })
    })

    it('should go back to warning step when back is clicked', async () => {
      const user = userEvent.setup()
      
      await user.click(screen.getByText('Back'))
      
      await waitFor(() => {
        expect(screen.getByText('Age Verification Required')).toBeInTheDocument()
      })
    })

    it('should calculate age precisely including months and days', async () => {
      const user = userEvent.setup()
      
      // Test edge case: birthday hasn't occurred this year yet
      const today = new Date()
      const thisYear = today.getFullYear()
      const nextMonth = today.getMonth() + 1
      const birthYear = thisYear - 21
      
      // Create a date that would make them 20 if birthday hasn't occurred
      const futureBirthday = `${birthYear}-${String(nextMonth).padStart(2, '0')}-15`
      
      const dateInput = screen.getByLabelText('Date of Birth')
      await user.type(dateInput, futureBirthday)
      
      await user.click(screen.getByText('Verify Age'))
      
      await waitFor(() => {
        expect(screen.getByText('You must be 21 years or older to access this site')).toBeInTheDocument()
      })
    })
  })

  describe('success step', () => {
    it('should show success message and auto-proceed', async () => {
      const user = userEvent.setup()
      
      mockVerifyAge.mockResolvedValue(undefined)
      
      render(<AgeVerification onVerified={mockOnVerified} />)
      
      // Navigate to verification step
      await user.click(screen.getByText('I am 21 or older - Continue'))
      
      await waitFor(() => {
        expect(screen.getByText('Verify Your Age')).toBeInTheDocument()
      })
      
      // Enter valid age and verify
      const dateInput = screen.getByLabelText('Date of Birth')
      await user.type(dateInput, '1990-01-01')
      
      await user.click(screen.getByText('Verify Age'))
      
      // Should show success step
      await waitFor(() => {
        expect(screen.getByText('Age Verified!')).toBeInTheDocument()
      })
      
      // Should auto-proceed after 2 seconds
      await waitFor(() => {
        expect(mockOnVerified).toHaveBeenCalled()
      }, { timeout: 3000 })
    })
  })
})

describe('useAgeVerification hook', () => {
  it('should return correct verification status for verified user', () => {
    mockUseAuth.mockReturnValue({
      user: {
        id: '1',
        email: '<EMAIL>',
        firstName: 'Test',
        lastName: 'User',
        ageVerified: true,
        idVerified: false,
        role: 'CUSTOMER',
        createdAt: new Date(),
        updatedAt: new Date()
      },
      session: null,
      isLoading: false,
      login: jest.fn(),
      register: jest.fn(),
      logout: jest.fn(),
      updateProfile: jest.fn(),
      verifyAge: jest.fn(),
      uploadIdDocument: jest.fn()
    })
    
    const TestComponent = () => {
      const { isAgeVerified, needsVerification } = useAgeVerification()
      
      return (
        <div>
          <div data-testid="verified">{isAgeVerified ? 'Verified' : 'Not Verified'}</div>
          <div data-testid="needs">{needsVerification ? 'Needs Verification' : 'No Verification Needed'}</div>
        </div>
      )
    }
    
    render(<TestComponent />)
    
    expect(screen.getByTestId('verified')).toHaveTextContent('Verified')
    expect(screen.getByTestId('needs')).toHaveTextContent('No Verification Needed')
  })

  it('should return correct verification status for unverified user', () => {
    mockUseAuth.mockReturnValue({
      user: {
        id: '1',
        email: '<EMAIL>',
        firstName: 'Test',
        lastName: 'User',
        ageVerified: false,
        idVerified: false,
        role: 'CUSTOMER',
        createdAt: new Date(),
        updatedAt: new Date()
      },
      session: null,
      isLoading: false,
      login: jest.fn(),
      register: jest.fn(),
      logout: jest.fn(),
      updateProfile: jest.fn(),
      verifyAge: jest.fn(),
      uploadIdDocument: jest.fn()
    })
    
    const TestComponent = () => {
      const { isAgeVerified, needsVerification } = useAgeVerification()
      
      return (
        <div>
          <div data-testid="verified">{isAgeVerified ? 'Verified' : 'Not Verified'}</div>
          <div data-testid="needs">{needsVerification ? 'Needs Verification' : 'No Verification Needed'}</div>
        </div>
      )
    }
    
    render(<TestComponent />)
    
    expect(screen.getByTestId('verified')).toHaveTextContent('Not Verified')
    expect(screen.getByTestId('needs')).toHaveTextContent('Needs Verification')
  })
})

describe('withAgeVerification HOC', () => {
  const TestComponent = () => <div>Protected Content</div>
  const ProtectedComponent = withAgeVerification(TestComponent)

  it('should render component for verified user', () => {
    mockUseAuth.mockReturnValue({
      user: {
        id: '1',
        email: '<EMAIL>',
        firstName: 'Test',
        lastName: 'User',
        ageVerified: true,
        idVerified: false,
        role: 'CUSTOMER',
        createdAt: new Date(),
        updatedAt: new Date()
      },
      session: null,
      isLoading: false,
      login: jest.fn(),
      register: jest.fn(),
      logout: jest.fn(),
      updateProfile: jest.fn(),
      verifyAge: jest.fn(),
      uploadIdDocument: jest.fn()
    })
    
    render(<ProtectedComponent />)
    
    expect(screen.getByText('Protected Content')).toBeInTheDocument()
  })

  it('should render age verification for unverified user', () => {
    mockUseAuth.mockReturnValue({
      user: {
        id: '1',
        email: '<EMAIL>',
        firstName: 'Test',
        lastName: 'User',
        ageVerified: false,
        idVerified: false,
        role: 'CUSTOMER',
        createdAt: new Date(),
        updatedAt: new Date()
      },
      session: null,
      isLoading: false,
      login: jest.fn(),
      register: jest.fn(),
      logout: jest.fn(),
      updateProfile: jest.fn(),
      verifyAge: jest.fn(),
      uploadIdDocument: jest.fn()
    })
    
    render(<ProtectedComponent />)
    
    expect(screen.getByText('Age Verification Required')).toBeInTheDocument()
    expect(screen.queryByText('Protected Content')).not.toBeInTheDocument()
  })
})
