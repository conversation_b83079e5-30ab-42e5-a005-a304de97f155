import { MetadataRoute } from 'next'

export default function sitemap(): MetadataRoute.Sitemap {
  const baseUrl = process.env.NEXT_PUBLIC_APP_URL || 'https://weednearmedc.com'
  
  // Static pages
  const staticPages = [
    {
      url: baseUrl,
      lastModified: new Date(),
      changeFrequency: 'daily' as const,
      priority: 1,
    },
    {
      url: `${baseUrl}/about`,
      lastModified: new Date(),
      changeFrequency: 'monthly' as const,
      priority: 0.8,
    },
    {
      url: `${baseUrl}/products`,
      lastModified: new Date(),
      changeFrequency: 'daily' as const,
      priority: 0.9,
    },
    {
      url: `${baseUrl}/locations`,
      lastModified: new Date(),
      changeFrequency: 'weekly' as const,
      priority: 0.9,
    },
    {
      url: `${baseUrl}/delivery`,
      lastModified: new Date(),
      changeFrequency: 'weekly' as const,
      priority: 0.8,
    },
    {
      url: `${baseUrl}/deals`,
      lastModified: new Date(),
      changeFrequency: 'daily' as const,
      priority: 0.7,
    },
    {
      url: `${baseUrl}/contact`,
      lastModified: new Date(),
      changeFrequency: 'monthly' as const,
      priority: 0.6,
    },
    {
      url: `${baseUrl}/privacy`,
      lastModified: new Date(),
      changeFrequency: 'yearly' as const,
      priority: 0.3,
    },
    {
      url: `${baseUrl}/terms`,
      lastModified: new Date(),
      changeFrequency: 'yearly' as const,
      priority: 0.3,
    },
  ]

  // Location-based pages for SEO
  const locations = [
    'washington-dc',
    'alexandria-va',
    'arlington-va',
    'bethesda-md',
    'silver-spring-md',
    'rockville-md',
    'falls-church-va',
    'fairfax-va',
    'vienna-va',
    'mclean-va',
    'potomac-md',
    'chevy-chase-md',
    'takoma-park-md',
    'college-park-md',
    'hyattsville-md'
  ]

  const locationPages = locations.map(location => ({
    url: `${baseUrl}/locations/${location}`,
    lastModified: new Date(),
    changeFrequency: 'weekly' as const,
    priority: 0.8,
  }))

  // Product category pages
  const categories = [
    'flower',
    'edibles',
    'concentrates',
    'vapes',
    'topicals',
    'accessories',
    'pre-rolls',
    'tinctures',
    'capsules'
  ]

  const categoryPages = categories.map(category => ({
    url: `${baseUrl}/products/${category}`,
    lastModified: new Date(),
    changeFrequency: 'daily' as const,
    priority: 0.7,
  }))

  // Service area pages for local SEO
  const serviceAreas = [
    'cannabis-delivery-washington-dc',
    'weed-delivery-alexandria-va',
    'marijuana-delivery-arlington-va',
    'cannabis-delivery-bethesda-md',
    'weed-delivery-silver-spring-md',
    'marijuana-delivery-rockville-md'
  ]

  const serviceAreaPages = serviceAreas.map(area => ({
    url: `${baseUrl}/delivery/${area}`,
    lastModified: new Date(),
    changeFrequency: 'weekly' as const,
    priority: 0.8,
  }))

  // Blog/content pages (if you have a blog)
  const blogPages = [
    {
      url: `${baseUrl}/blog`,
      lastModified: new Date(),
      changeFrequency: 'weekly' as const,
      priority: 0.6,
    },
    {
      url: `${baseUrl}/blog/cannabis-guide`,
      lastModified: new Date(),
      changeFrequency: 'monthly' as const,
      priority: 0.5,
    },
    {
      url: `${baseUrl}/blog/delivery-guide`,
      lastModified: new Date(),
      changeFrequency: 'monthly' as const,
      priority: 0.5,
    },
    {
      url: `${baseUrl}/blog/product-reviews`,
      lastModified: new Date(),
      changeFrequency: 'weekly' as const,
      priority: 0.5,
    }
  ]

  return [
    ...staticPages,
    ...locationPages,
    ...categoryPages,
    ...serviceAreaPages,
    ...blogPages
  ]
}
