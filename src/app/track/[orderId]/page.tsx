import { DeliveryTracker } from "@/components/delivery/delivery-tracker"
import { MainLayout } from "@/components/layout/main-layout"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Badge } from "@/components/ui/badge"
import { ArrowLeft, Package, DollarSign } from "lucide-react"
import Link from "next/link"

// Mock order data (in production, this would come from your database)
const mockOrder = {
  id: "order_123",
  orderNumber: "WND-2024-001",
  status: "OUT_FOR_DELIVERY" as const,
  total: 8750, // $87.50 in cents
  subtotal: 7500,
  tax: 750,
  tip: 500,
  deliveryFee: 0,
  estimatedDelivery: new Date(Date.now() + 25 * 60 * 1000), // 25 minutes from now
  driverName: "<PERSON> Johnson",
  driverPhone: "******-555-0123",
  deliveryAddress: "1234 Main St, Washington, DC 20001",
  items: [
    {
      id: 1,
      name: "<PERSON> Dream",
      brand: "West Coast Cure",
      quantity: 1,
      price: 5500,
      weight: "3.5g"
    },
    {
      id: 2,
      name: "Sour Diesel Gummies",
      brand: "Kiva",
      quantity: 2,
      price: 1000,
      weight: "10mg x 10"
    }
  ],
  createdAt: new Date(Date.now() - 2 * 60 * 60 * 1000), // 2 hours ago
}

interface TrackOrderPageProps {
  params: Promise<{
    orderId: string
  }>
}

export default async function TrackOrderPage({ params }: TrackOrderPageProps) {
  const { orderId } = await params

  // In production, you would fetch the order data based on orderId
  // const order = await getOrderById(orderId)

  return (
    <MainLayout>
      <div className="min-h-screen bg-gray-50 py-8">
        <div className="container mx-auto px-4">
          {/* Header */}
          <div className="mb-8">
            <Button variant="ghost" asChild className="mb-4">
              <Link href="/account">
                <ArrowLeft className="h-4 w-4 mr-2" />
                Back to Orders
              </Link>
            </Button>
            <h1 className="text-3xl font-bold text-gray-900">Track Your Order</h1>
            <p className="text-gray-600 mt-2">
              Real-time updates for order #{mockOrder.orderNumber}
            </p>
          </div>

          <div className="grid lg:grid-cols-3 gap-8">
            {/* Main Tracking Content */}
            <div className="lg:col-span-2">
              <DeliveryTracker
                orderId={mockOrder.id}
                orderNumber={mockOrder.orderNumber}
                currentStatus={mockOrder.status}
                estimatedDelivery={mockOrder.estimatedDelivery}
                driverName={mockOrder.driverName}
                driverPhone={mockOrder.driverPhone}
              />
            </div>

            {/* Order Summary Sidebar */}
            <div className="space-y-6">
              {/* Order Details */}
              <Card>
                <CardHeader>
                  <CardTitle className="flex items-center gap-2">
                    <Package className="h-5 w-5" />
                    Order Details
                  </CardTitle>
                  <CardDescription>
                    Placed {mockOrder.createdAt.toLocaleDateString()} at{" "}
                    {mockOrder.createdAt.toLocaleTimeString('en-US', {
                      hour: 'numeric',
                      minute: '2-digit',
                      hour12: true
                    })}
                  </CardDescription>
                </CardHeader>
                <CardContent>
                  <div className="space-y-4">
                    {/* Items */}
                    <div>
                      <h4 className="font-medium mb-3">Items ({mockOrder.items.length})</h4>
                      <div className="space-y-3">
                        {mockOrder.items.map((item) => (
                          <div key={item.id} className="flex justify-between items-start">
                            <div className="flex-1">
                              <div className="font-medium">{item.name}</div>
                              <div className="text-sm text-gray-600">
                                {item.brand} • {item.weight}
                              </div>
                              <div className="text-sm text-gray-500">
                                Qty: {item.quantity}
                              </div>
                            </div>
                            <div className="text-right">
                              <div className="font-medium">
                                ${(item.price * item.quantity / 100).toFixed(2)}
                              </div>
                            </div>
                          </div>
                        ))}
                      </div>
                    </div>

                    {/* Delivery Address */}
                    <div className="pt-4 border-t">
                      <h4 className="font-medium mb-2">Delivery Address</h4>
                      <p className="text-sm text-gray-600">
                        {mockOrder.deliveryAddress}
                      </p>
                    </div>
                  </div>
                </CardContent>
              </Card>

              {/* Order Total */}
              <Card>
                <CardHeader>
                  <CardTitle className="flex items-center gap-2">
                    <DollarSign className="h-5 w-5" />
                    Order Total
                  </CardTitle>
                </CardHeader>
                <CardContent>
                  <div className="space-y-2">
                    <div className="flex justify-between">
                      <span>Subtotal</span>
                      <span>${(mockOrder.subtotal / 100).toFixed(2)}</span>
                    </div>
                    <div className="flex justify-between">
                      <span>Tax</span>
                      <span>${(mockOrder.tax / 100).toFixed(2)}</span>
                    </div>
                    <div className="flex justify-between">
                      <span>Tip</span>
                      <span>${(mockOrder.tip / 100).toFixed(2)}</span>
                    </div>
                    <div className="flex justify-between">
                      <span>Delivery Fee</span>
                      <span>
                        {mockOrder.deliveryFee === 0 ? (
                          <Badge variant="secondary" className="bg-green-100 text-green-800">
                            FREE
                          </Badge>
                        ) : (
                          `$${(mockOrder.deliveryFee / 100).toFixed(2)}`
                        )}
                      </span>
                    </div>
                    <div className="border-t pt-2">
                      <div className="flex justify-between font-bold text-lg">
                        <span>Total</span>
                        <span>${(mockOrder.total / 100).toFixed(2)}</span>
                      </div>
                    </div>
                  </div>
                </CardContent>
              </Card>

              {/* Support */}
              <Card>
                <CardHeader>
                  <CardTitle>Need Help?</CardTitle>
                </CardHeader>
                <CardContent>
                  <div className="space-y-3">
                    <Button variant="outline" className="w-full" asChild>
                      <a href="tel:******-555-WEED">
                        Call Support
                      </a>
                    </Button>
                    <Button variant="outline" className="w-full" asChild>
                      <Link href="/contact">
                        Contact Us
                      </Link>
                    </Button>
                    <Button variant="outline" className="w-full" asChild>
                      <Link href="/faq">
                        View FAQ
                      </Link>
                    </Button>
                  </div>
                </CardContent>
              </Card>
            </div>
          </div>
        </div>
      </div>
    </MainLayout>
  )
}
