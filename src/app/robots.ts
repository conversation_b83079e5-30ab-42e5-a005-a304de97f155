import { MetadataRoute } from 'next'

export default function robots(): MetadataRoute.Robots {
  const baseUrl = process.env.NEXT_PUBLIC_APP_URL || 'https://weednearmedc.com'
  
  return {
    rules: [
      {
        userAgent: '*',
        allow: '/',
        disallow: [
          '/admin/',
          '/api/',
          '/auth/',
          '/dashboard/',
          '/checkout/',
          '/account/',
          '/orders/',
          '/cart/',
          '/_next/',
          '/private/',
          '/temp/',
          '*.json',
          '*.xml',
          '/search?*',
          '/filter?*'
        ],
      },
      {
        userAgent: 'Googlebot',
        allow: '/',
        disallow: [
          '/admin/',
          '/api/',
          '/auth/',
          '/dashboard/',
          '/checkout/',
          '/account/',
          '/orders/',
          '/cart/',
          '/_next/',
          '/private/',
          '/temp/'
        ],
      },
      {
        userAgent: 'Bingbot',
        allow: '/',
        disallow: [
          '/admin/',
          '/api/',
          '/auth/',
          '/dashboard/',
          '/checkout/',
          '/account/',
          '/orders/',
          '/cart/',
          '/_next/',
          '/private/',
          '/temp/'
        ],
      }
    ],
    sitemap: `${baseUrl}/sitemap.xml`,
    host: baseUrl,
  }
}
