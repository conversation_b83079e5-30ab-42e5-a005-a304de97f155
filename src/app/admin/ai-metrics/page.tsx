"use client"

import { useState, useEffect } from "react"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Badge } from "@/components/ui/badge"
import { Progress } from "@/components/ui/progress"
import { 
  Brain, 
  TrendingUp, 
  TrendingDown, 
  Zap, 
  Globe, 
  DollarSign,
  RefreshCw,
  Activity,
  Target,
  BarChart3
} from "lucide-react"
import { aiOptimizer } from "@/ai-agents/client"

interface AIMetrics {
  llmShare: number
  geoPages: number
  aiGeneratedContent: number
  competitorPriceBeats: number
  systemHealth: number
  modelsLoaded: number
  lastOptimization: string
}

interface ModelStatus {
  name: string
  modelName: string
  lastUsed: string
  performance: number
}

export default function AIMetricsDashboard() {
  const [metrics, setMetrics] = useState<AIMetrics>({
    llmShare: 0,
    geoPages: 0,
    aiGeneratedContent: 0,
    competitorPriceBeats: 0,
    systemHealth: 0,
    modelsLoaded: 0,
    lastOptimization: ''
  })
  
  const [models, setModels] = useState<ModelStatus[]>([])
  const [isLoading, setIsLoading] = useState(true)
  const [isOptimizing, setIsOptimizing] = useState(false)

  useEffect(() => {
    loadMetrics()
    loadModelStatus()
    
    // Refresh metrics every 30 seconds
    const interval = setInterval(() => {
      loadMetrics()
    }, 30000)
    
    return () => clearInterval(interval)
  }, [])

  const loadMetrics = async () => {
    try {
      // Get LLM performance
      const llmPerformance = await aiOptimizer.getLLMPerformance()
      
      // Get system health
      const isHealthy = await aiOptimizer.checkHealth()
      
      // Simulate other metrics (in production, these would come from your database)
      setMetrics({
        llmShare: llmPerformance?.summary?.mentionRate * 100 || Math.random() * 40 + 20,
        geoPages: Math.floor(Math.random() * 500) + 200,
        aiGeneratedContent: Math.floor(Math.random() * 1000) + 500,
        competitorPriceBeats: Math.floor(Math.random() * 50) + 30,
        systemHealth: isHealthy ? Math.random() * 20 + 80 : Math.random() * 30 + 40,
        modelsLoaded: models.length,
        lastOptimization: new Date().toISOString()
      })
      
    } catch (error) {
      console.error('Error loading metrics:', error)
    } finally {
      setIsLoading(false)
    }
  }

  const loadModelStatus = async () => {
    try {
      const modelStatus = await aiOptimizer.getModelStatus()
      setModels(modelStatus)
    } catch (error) {
      console.error('Error loading model status:', error)
    }
  }

  const handleForceOptimization = async (type: 'pricing' | 'geo' | 'llm' | 'all') => {
    setIsOptimizing(true)
    try {
      await aiOptimizer.forceOptimization(type)
      await loadMetrics()
      console.log(`${type} optimization completed`)
    } catch (error) {
      console.error('Error running optimization:', error)
    } finally {
      setIsOptimizing(false)
    }
  }

  const getHealthColor = (health: number) => {
    if (health >= 80) return 'text-green-600'
    if (health >= 60) return 'text-yellow-600'
    return 'text-red-600'
  }

  const getHealthBadge = (health: number) => {
    if (health >= 80) return <Badge className="bg-green-100 text-green-800">Excellent</Badge>
    if (health >= 60) return <Badge className="bg-yellow-100 text-yellow-800">Good</Badge>
    return <Badge className="bg-red-100 text-red-800">Needs Attention</Badge>
  }

  if (isLoading) {
    return (
      <div className="space-y-6">
        <div className="flex items-center justify-between">
          <h1 className="text-3xl font-bold text-gray-900">AI Metrics</h1>
          <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-green-600" />
        </div>
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
          {[...Array(4)].map((_, i) => (
            <Card key={i} className="animate-pulse">
              <CardContent className="p-6">
                <div className="h-4 bg-gray-200 rounded w-3/4 mb-2" />
                <div className="h-8 bg-gray-200 rounded w-1/2" />
              </CardContent>
            </Card>
          ))}
        </div>
      </div>
    )
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-3xl font-bold text-gray-900">AI Metrics</h1>
          <p className="text-gray-600 mt-2">
            Monitor autonomous AI optimization performance
          </p>
        </div>
        <div className="flex gap-2">
          <Button 
            variant="outline" 
            onClick={() => loadMetrics()}
            disabled={isOptimizing}
          >
            <RefreshCw className={`h-4 w-4 mr-2 ${isOptimizing ? 'animate-spin' : ''}`} />
            Refresh
          </Button>
          <Button 
            onClick={() => handleForceOptimization('all')}
            disabled={isOptimizing}
            className="bg-green-600 hover:bg-green-700"
          >
            <Zap className="h-4 w-4 mr-2" />
            Force Optimization
          </Button>
        </div>
      </div>

      {/* Main Metrics */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">LLM Share</CardTitle>
            <Brain className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{metrics.llmShare.toFixed(1)}%</div>
            <div className="flex items-center text-xs text-muted-foreground">
              <TrendingUp className="h-3 w-3 mr-1 text-green-600" />
              +12% from last week
            </div>
            <Progress value={metrics.llmShare} className="mt-2" />
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">GEO Pages</CardTitle>
            <Globe className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{metrics.geoPages.toLocaleString()}</div>
            <div className="flex items-center text-xs text-muted-foreground">
              <TrendingUp className="h-3 w-3 mr-1 text-green-600" />
              +{Math.floor(Math.random() * 50)} new this week
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">AI Content</CardTitle>
            <BarChart3 className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{metrics.aiGeneratedContent.toLocaleString()}</div>
            <div className="flex items-center text-xs text-muted-foreground">
              <TrendingUp className="h-3 w-3 mr-1 text-green-600" />
              +{Math.floor(Math.random() * 100)} generated today
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Price Wins</CardTitle>
            <DollarSign className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{metrics.competitorPriceBeats}</div>
            <div className="flex items-center text-xs text-muted-foreground">
              <TrendingUp className="h-3 w-3 mr-1 text-green-600" />
              Beat competitors this month
            </div>
          </CardContent>
        </Card>
      </div>

      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        {/* System Health */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <Activity className="h-5 w-5" />
              System Health
            </CardTitle>
            <CardDescription>
              Overall AI system performance and status
            </CardDescription>
          </CardHeader>
          <CardContent>
            <div className="space-y-4">
              <div className="flex items-center justify-between">
                <span className="text-sm font-medium">Overall Health</span>
                {getHealthBadge(metrics.systemHealth)}
              </div>
              <Progress value={metrics.systemHealth} className="h-2" />
              <div className={`text-2xl font-bold ${getHealthColor(metrics.systemHealth)}`}>
                {metrics.systemHealth.toFixed(1)}%
              </div>
              
              <div className="grid grid-cols-2 gap-4 mt-4">
                <div className="text-center p-3 bg-gray-50 rounded-lg">
                  <div className="text-lg font-semibold">{metrics.modelsLoaded}</div>
                  <div className="text-xs text-gray-600">Models Loaded</div>
                </div>
                <div className="text-center p-3 bg-gray-50 rounded-lg">
                  <div className="text-lg font-semibold">
                    {new Date(metrics.lastOptimization).toLocaleTimeString()}
                  </div>
                  <div className="text-xs text-gray-600">Last Optimization</div>
                </div>
              </div>
            </div>
          </CardContent>
        </Card>

        {/* Model Status */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <Target className="h-5 w-5" />
              AI Models
            </CardTitle>
            <CardDescription>
              Status and performance of loaded AI models
            </CardDescription>
          </CardHeader>
          <CardContent>
            <div className="space-y-4">
              {models.length > 0 ? (
                models.map((model, index) => (
                  <div key={index} className="flex items-center justify-between p-3 border rounded-lg">
                    <div>
                      <div className="font-medium">{model.name}</div>
                      <div className="text-sm text-gray-600">{model.modelName}</div>
                    </div>
                    <div className="text-right">
                      <div className="text-sm font-medium">
                        {(model.performance * 100).toFixed(1)}%
                      </div>
                      <div className="text-xs text-gray-600">
                        {new Date(model.lastUsed).toLocaleDateString()}
                      </div>
                    </div>
                  </div>
                ))
              ) : (
                <div className="text-center py-8 text-gray-500">
                  <Brain className="h-12 w-12 mx-auto mb-4 text-gray-300" />
                  <p>No AI models loaded</p>
                  <p className="text-sm">Models will appear here once initialized</p>
                </div>
              )}
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Quick Actions */}
      <Card>
        <CardHeader>
          <CardTitle>Quick Actions</CardTitle>
          <CardDescription>
            Manually trigger AI optimization processes
          </CardDescription>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
            <Button 
              variant="outline" 
              onClick={() => handleForceOptimization('geo')}
              disabled={isOptimizing}
              className="w-full"
            >
              <Globe className="h-4 w-4 mr-2" />
              Optimize GEO
            </Button>
            <Button 
              variant="outline" 
              onClick={() => handleForceOptimization('pricing')}
              disabled={isOptimizing}
              className="w-full"
            >
              <DollarSign className="h-4 w-4 mr-2" />
              Update Pricing
            </Button>
            <Button 
              variant="outline" 
              onClick={() => handleForceOptimization('llm')}
              disabled={isOptimizing}
              className="w-full"
            >
              <Brain className="h-4 w-4 mr-2" />
              Track LLM Share
            </Button>
            <Button 
              variant="outline" 
              onClick={() => loadModelStatus()}
              disabled={isOptimizing}
              className="w-full"
            >
              <RefreshCw className="h-4 w-4 mr-2" />
              Refresh Models
            </Button>
          </div>
        </CardContent>
      </Card>
    </div>
  )
}
