"use client"

import { useState, useEffect } from 'react'
import { useRouter } from 'next/navigation'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { Button } from "@/components/ui/button"
import { Alert, AlertDescription } from "@/components/ui/alert"
import { Mail, CheckCircle, RefreshCw, ArrowLeft } from "lucide-react"
import { supabase } from '@/lib/supabase/client'

export default function VerifyEmail() {
  const router = useRouter()
  const [isResending, setIsResending] = useState(false)
  const [resendMessage, setResendMessage] = useState('')
  const [email, setEmail] = useState('')

  useEffect(() => {
    // Get email from session or URL params
    const getEmail = async () => {
      const { data: { session } } = await supabase.auth.getSession()
      if (session?.user?.email) {
        setEmail(session.user.email)
      }
    }
    
    getEmail()
  }, [])

  const handleResendEmail = async () => {
    if (!email) {
      setResendMessage('No email address found. Please try registering again.')
      return
    }

    setIsResending(true)
    setResendMessage('')

    try {
      const { error } = await supabase.auth.resend({
        type: 'signup',
        email: email
      })

      if (error) {
        throw new Error(error.message)
      }

      setResendMessage('Verification email sent! Please check your inbox.')
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : 'Failed to resend email'
      setResendMessage(errorMessage)
    } finally {
      setIsResending(false)
    }
  }

  const handleBackToLogin = () => {
    router.push('/auth/login')
  }

  const handleGoHome = () => {
    router.push('/')
  }

  return (
    <div className="min-h-screen bg-gradient-to-br from-green-50 to-emerald-100 flex items-center justify-center p-4">
      <Card className="w-full max-w-md">
        <CardHeader className="text-center">
          <div className="mx-auto w-16 h-16 bg-blue-100 rounded-full flex items-center justify-center mb-4">
            <Mail className="w-8 h-8 text-blue-600" />
          </div>
          <CardTitle className="text-2xl font-bold">Check Your Email</CardTitle>
          <CardDescription>
            We've sent a verification link to your email address
          </CardDescription>
        </CardHeader>
        
        <CardContent className="space-y-6">
          {email && (
            <div className="p-4 bg-blue-50 rounded-lg">
              <p className="text-sm text-blue-800 text-center">
                <strong>Email sent to:</strong><br />
                {email}
              </p>
            </div>
          )}

          <div className="space-y-4">
            <div className="flex items-start space-x-3">
              <CheckCircle className="w-5 h-5 text-green-600 mt-0.5 flex-shrink-0" />
              <div>
                <p className="text-sm font-medium">Check your inbox</p>
                <p className="text-xs text-gray-600">
                  Click the verification link in the email we sent you
                </p>
              </div>
            </div>

            <div className="flex items-start space-x-3">
              <CheckCircle className="w-5 h-5 text-green-600 mt-0.5 flex-shrink-0" />
              <div>
                <p className="text-sm font-medium">Check spam folder</p>
                <p className="text-xs text-gray-600">
                  Sometimes emails end up in spam or junk folders
                </p>
              </div>
            </div>

            <div className="flex items-start space-x-3">
              <CheckCircle className="w-5 h-5 text-green-600 mt-0.5 flex-shrink-0" />
              <div>
                <p className="text-sm font-medium">Click the link</p>
                <p className="text-xs text-gray-600">
                  The verification link will activate your account
                </p>
              </div>
            </div>
          </div>

          {resendMessage && (
            <Alert className={resendMessage.includes('sent') ? 'border-green-200 bg-green-50' : 'border-red-200 bg-red-50'}>
              <AlertDescription className={resendMessage.includes('sent') ? 'text-green-800' : 'text-red-800'}>
                {resendMessage}
              </AlertDescription>
            </Alert>
          )}

          <div className="space-y-3">
            <Button 
              onClick={handleResendEmail}
              disabled={isResending || !email}
              variant="outline"
              className="w-full"
            >
              {isResending ? (
                <>
                  <RefreshCw className="w-4 h-4 mr-2 animate-spin" />
                  Sending...
                </>
              ) : (
                <>
                  <RefreshCw className="w-4 h-4 mr-2" />
                  Resend Email
                </>
              )}
            </Button>

            <Button 
              onClick={handleBackToLogin}
              variant="ghost"
              className="w-full"
            >
              <ArrowLeft className="w-4 h-4 mr-2" />
              Back to Login
            </Button>

            <Button 
              onClick={handleGoHome}
              variant="ghost"
              className="w-full text-xs"
            >
              Go Home
            </Button>
          </div>

          <div className="p-4 bg-gray-50 rounded-lg">
            <p className="text-xs text-gray-600 text-center">
              <strong>Having trouble?</strong><br />
              Make sure to check your spam folder and add our email to your contacts.
              The verification link expires in 24 hours.
            </p>
          </div>
        </CardContent>
      </Card>
    </div>
  )
}
