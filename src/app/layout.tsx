import type { Metadata } from "next";
import { Inter } from "next/font/google";
import "./globals.css";
import { ThemeProvider } from "@/components/theme-provider";
import { TRPCProvider } from "@/components/providers/trpc-provider";
import { AuthProvider } from "@/components/providers/auth-provider";
import { PerformanceProvider, PerformanceDebugger, PerformanceWarning, ResourceHints } from "@/components/performance/performance-provider";
import { Toaster } from "@/components/ui/sonner";
import { SEO_DEFAULTS } from "@/constants";

const inter = Inter({
  subsets: ["latin"],
  variable: "--font-inter",
});

export const metadata: Metadata = {
  metadataBase: new URL(process.env.NEXT_PUBLIC_APP_URL || 'https://weednearmedc.com'),
  title: {
    default: SEO_DEFAULTS.title,
    template: `%s | ${SEO_DEFAULTS.title}`,
  },
  description: SEO_DEFAULTS.description,
  keywords: SEO_DEFAULTS.keywords,
  authors: [{ name: "WeedNearMeDC" }],
  creator: "WeedNearMeDC",
  openGraph: {
    type: "website",
    locale: "en_US",
    url: "https://weednearmedc.com",
    title: SEO_DEFAULTS.title,
    description: SEO_DEFAULTS.description,
    siteName: "WeedNearMeDC",
    images: [
      {
        url: SEO_DEFAULTS.ogImage,
        width: 1200,
        height: 630,
        alt: "WeedNearMeDC - Premium Cannabis Delivery",
      },
    ],
  },
  twitter: {
    card: "summary_large_image",
    title: SEO_DEFAULTS.title,
    description: SEO_DEFAULTS.description,
    images: [SEO_DEFAULTS.ogImage],
  },
  robots: {
    index: true,
    follow: true,
    googleBot: {
      index: true,
      follow: true,
      "max-video-preview": -1,
      "max-image-preview": "large",
      "max-snippet": -1,
    },
  },
  verification: {
    google: "your-google-verification-code",
  },
};

export default function RootLayout({
  children,
}: Readonly<{
  children: React.ReactNode;
}>) {
  return (
    <html lang="en" suppressHydrationWarning>
      <body className={`${inter.variable} font-sans antialiased`}>
        <ThemeProvider
          attribute="class"
          defaultTheme="light"
          enableSystem
          disableTransitionOnChange
        >
          <PerformanceProvider>
            <AuthProvider>
              <TRPCProvider>
                <ResourceHints />
                {children}
                <Toaster />
                <PerformanceDebugger />
                <PerformanceWarning />
              </TRPCProvider>
            </AuthProvider>
          </PerformanceProvider>
        </ThemeProvider>
      </body>
    </html>
  );
}
