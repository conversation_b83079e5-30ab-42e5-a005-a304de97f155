import { NextRequest, NextResponse } from 'next/server';
import { createClient } from '@supabase/supabase-js';
import { deploymentAdapter } from '@/ai-agents/deployment-adapter';

// Use Node.js runtime for database operations
export const runtime = 'nodejs';

// Initialize Supabase client
const supabase = createClient(
  process.env.NEXT_PUBLIC_SUPABASE_URL!,
  process.env.SUPABASE_SERVICE_ROLE_KEY!
);

interface LLMTrackingData {
  query: string;
  llmSource: string;
  position?: number;
  mentioned: boolean;
  url?: string;
  timestamp: string;
}

export async function POST(request: NextRequest) {
  try {
    const { operation, data } = await request.json();
    
    switch (operation) {
      case 'track-mention': {
        if (!data || !data.query || !data.llmSource) {
          return NextResponse.json(
            { success: false, error: 'Invalid tracking data' },
            { status: 400 }
          );
        }
        
        const trackingData: LLMTrackingData = {
          query: data.query,
          llmSource: data.llmSource,
          position: data.position,
          mentioned: data.mentioned,
          url: data.url,
          timestamp: new Date().toISOString()
        };
        
        // Store tracking data in Supabase
        const { error } = await supabase
          .from('llm_tracking')
          .insert(trackingData);
          
        if (error) {
          throw new Error(`Failed to store tracking data: ${error.message}`);
        }
        
        return NextResponse.json({
          success: true,
          message: 'LLM mention tracked successfully',
          timestamp: new Date().toISOString()
        });
      }
      
      case 'batch-track': {
        if (!Array.isArray(data) || data.length === 0) {
          return NextResponse.json(
            { success: false, error: 'Invalid batch tracking data' },
            { status: 400 }
          );
        }
        
        const trackingData = data.map(item => ({
          query: item.query,
          llmSource: item.llmSource,
          position: item.position,
          mentioned: item.mentioned,
          url: item.url,
          timestamp: new Date().toISOString()
        }));
        
        // Store batch tracking data in Supabase
        const { error } = await supabase
          .from('llm_tracking')
          .insert(trackingData);
          
        if (error) {
          throw new Error(`Failed to store batch tracking data: ${error.message}`);
        }
        
        return NextResponse.json({
          success: true,
          message: `${trackingData.length} LLM mentions tracked successfully`,
          timestamp: new Date().toISOString()
        });
      }
      
      case 'analyze-performance': {
        // Analyze LLM performance for a specific query or all queries
        const query = data?.query || null;
        
        let queryBuilder = supabase
          .from('llm_tracking')
          .select('*')
          .order('timestamp', { ascending: false });
          
        if (query) {
          queryBuilder = queryBuilder.eq('query', query);
        }
        
        const { data: trackingData, error } = await queryBuilder.limit(100);
        
        if (error) {
          throw new Error(`Failed to retrieve tracking data: ${error.message}`);
        }
        
        // Calculate performance metrics
        const totalEntries = trackingData.length;
        const mentionedEntries = trackingData.filter(entry => entry.mentioned).length;
        const mentionRate = totalEntries > 0 ? mentionedEntries / totalEntries : 0;
        
        // Group by LLM source
        const llmSources = trackingData.reduce((acc, entry) => {
          const source = entry.llmSource;
          if (!acc[source]) {
            acc[source] = { total: 0, mentioned: 0 };
          }
          acc[source].total += 1;
          if (entry.mentioned) {
            acc[source].mentioned += 1;
          }
          return acc;
        }, {} as Record<string, { total: number; mentioned: number }>);
        
        // Calculate mention rate by LLM source
        const llmPerformance = Object.entries(llmSources).map(([source, stats]) => ({
          source,
          total: stats.total,
          mentioned: stats.mentioned,
          mentionRate: stats.total > 0 ? stats.mentioned / stats.total : 0
        }));
        
        return NextResponse.json({
          success: true,
          performance: {
            totalEntries,
            mentionedEntries,
            mentionRate,
            llmPerformance
          },
          timestamp: new Date().toISOString()
        });
      }
      
      default:
        return NextResponse.json(
          { success: false, error: 'Invalid operation' },
          { status: 400 }
        );
    }
    
  } catch (error) {
    console.error('LLM Monitor Error:', error);
    
    return NextResponse.json(
      { 
        success: false, 
        error: 'LLM monitoring operation failed',
        details: error instanceof Error ? error.message : 'Unknown error'
      },
      { status: 500 }
    );
  }
}

export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url);
    const operation = searchParams.get('operation');
    
    switch (operation) {
      case 'summary': {
        // Get summary of LLM tracking data
        const { data, error } = await supabase
          .from('llm_tracking')
          .select('*')
          .order('timestamp', { ascending: false })
          .limit(100);
          
        if (error) {
          throw new Error(`Failed to retrieve tracking data: ${error.message}`);
        }
        
        // Calculate summary metrics
        const totalEntries = data.length;
        const mentionedEntries = data.filter(entry => entry.mentioned).length;
        const mentionRate = totalEntries > 0 ? mentionedEntries / totalEntries : 0;
        
        // Group by query
        const queries = data.reduce((acc, entry) => {
          const query = entry.query;
          if (!acc[query]) {
            acc[query] = { total: 0, mentioned: 0 };
          }
          acc[query].total += 1;
          if (entry.mentioned) {
            acc[query].mentioned += 1;
          }
          return acc;
        }, {} as Record<string, { total: number; mentioned: number }>);
        
        // Calculate mention rate by query
        const queryPerformance = Object.entries(queries).map(([query, stats]) => ({
          query,
          total: stats.total,
          mentioned: stats.mentioned,
          mentionRate: stats.total > 0 ? stats.mentioned / stats.total : 0
        })).sort((a, b) => b.mentionRate - a.mentionRate);
        
        return NextResponse.json({
          success: true,
          summary: {
            totalEntries,
            mentionedEntries,
            mentionRate,
            topQueries: queryPerformance.slice(0, 10)
          },
          timestamp: new Date().toISOString()
        });
      }
      
      case 'trends': {
        // Get LLM mention trends over time
        const days = parseInt(searchParams.get('days') || '30', 10);
        const startDate = new Date();
        startDate.setDate(startDate.getDate() - days);
        
        const { data, error } = await supabase
          .from('llm_tracking')
          .select('*')
          .gte('timestamp', startDate.toISOString());
          
        if (error) {
          throw new Error(`Failed to retrieve trend data: ${error.message}`);
        }
        
        // Group by day
        const dailyData = data.reduce((acc, entry) => {
          const date = new Date(entry.timestamp).toISOString().split('T')[0];
          if (!acc[date]) {
            acc[date] = { total: 0, mentioned: 0 };
          }
          acc[date].total += 1;
          if (entry.mentioned) {
            acc[date].mentioned += 1;
          }
          return acc;
        }, {} as Record<string, { total: number; mentioned: number }>);
        
        // Calculate daily mention rates
        const trends = Object.entries(dailyData).map(([date, stats]) => ({
          date,
          total: stats.total,
          mentioned: stats.mentioned,
          mentionRate: stats.total > 0 ? stats.mentioned / stats.total : 0
        })).sort((a, b) => a.date.localeCompare(b.date));
        
        return NextResponse.json({
          success: true,
          trends,
          timestamp: new Date().toISOString()
        });
      }
      
      default:
        return NextResponse.json(
          { success: false, error: 'Invalid operation' },
          { status: 400 }
        );
    }
    
  } catch (error) {
    console.error('LLM Monitor GET Error:', error);
    
    return NextResponse.json(
      { 
        success: false, 
        error: 'LLM monitoring data retrieval failed',
        details: error instanceof Error ? error.message : 'Unknown error'
      },
      { status: 500 }
    );
  }
}
