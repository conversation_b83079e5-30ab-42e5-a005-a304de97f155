import { NextRequest, NextResponse } from 'next/server';
import { aiModelManager } from '@/ai-agents/model-manager';

// Use Node.js runtime for AI model loading
export const runtime = 'nodejs';

export async function POST(request: NextRequest) {
  try {
    const { prompt, type, options = {} } = await request.json();

    if (!prompt) {
      return NextResponse.json(
        { success: false, error: 'Prompt is required' },
        { status: 400 }
      );
    }

    // Get the content generation model
    const model = await aiModelManager.getModel('content-writer', 'text-generation');
    
    // Set default generation parameters
    const generationParams = {
      max_new_tokens: options.maxTokens || 150,
      temperature: options.temperature || 0.7,
      top_p: options.topP || 0.9,
      do_sample: true,
      ...options
    };
    
    // Generate content based on type
    let formattedPrompt = prompt;
    
    switch (type) {
      case 'product-description':
        formattedPrompt = `Write a compelling cannabis product description for: ${prompt}
Include details about effects, flavor profile, potency, and ideal usage scenarios.
Keep it professional, informative, and appealing to cannabis consumers.
Length: 100-150 words`;
        break;
        
      case 'geo-snippet':
        formattedPrompt = `Create a concise answer about cannabis delivery in ${prompt}
Include delivery times, price range, and legal requirements.
Make it factual, helpful, and optimized for AI search engines.
Length: 80-120 words`;
        break;
        
      case 'blog-post':
        formattedPrompt = `Write an informative blog post about: ${prompt}
Include relevant facts, benefits, and educational content about cannabis.
Make it engaging, authoritative, and SEO-friendly.
Length: 300-400 words`;
        break;
        
      case 'faq-answer':
        formattedPrompt = `Answer this cannabis-related question: ${prompt}
Provide a clear, factual, and helpful response.
Include relevant details and keep it conversational.
Length: 50-100 words`;
        break;
    }
    
    // Generate content
    const result = await model(formattedPrompt, generationParams);
    
    // Clean up the generated text
    const generatedText = result[0].generated_text.trim();
    
    return NextResponse.json({
      success: true,
      content: generatedText,
      type,
      timestamp: new Date().toISOString()
    });
    
  } catch (error) {
    console.error('Content Generation Error:', error);
    
    return NextResponse.json(
      { 
        success: false, 
        error: 'Content generation failed',
        details: error instanceof Error ? error.message : 'Unknown error'
      },
      { status: 500 }
    );
  }
}

export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url);
    const operation = searchParams.get('operation');
    
    switch (operation) {
      case 'status':
        return NextResponse.json({
          success: true,
          modelLoaded: contentModel !== null,
          modelName: 'Xenova/LaMini-Flan-T5-248M',
          supportedTypes: [
            'product-description',
            'geo-snippet',
            'blog-post',
            'faq-answer'
          ],
          timestamp: new Date().toISOString()
        });
        
      case 'sample':
        const model = await getContentModel();
        const samplePrompt = "Write a short description of Blue Dream cannabis strain";
        const result = await model(samplePrompt, { max_new_tokens: 50 });
        
        return NextResponse.json({
          success: true,
          sample: result[0].generated_text.trim(),
          timestamp: new Date().toISOString()
        });
        
      default:
        return NextResponse.json(
          { success: false, error: 'Invalid operation' },
          { status: 400 }
        );
    }
    
  } catch (error) {
    console.error('Content API GET Error:', error);
    
    return NextResponse.json(
      { 
        success: false, 
        error: 'Content API status check failed',
        details: error instanceof Error ? error.message : 'Unknown error'
      },
      { status: 500 }
    );
  }
}
