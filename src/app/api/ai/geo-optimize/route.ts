import { NextRequest, NextResponse } from 'next/server';
import { WeedNearMeAIOrchestrator } from '@/ai-agents/orchestrator';

// Use Node.js runtime for AI model loading (edge runtime has memory limitations)
export const runtime = 'nodejs';

let aiOrchestrator: WeedNearMeAIOrchestrator | null = null;

async function getOrchestrator() {
  if (!aiOrchestrator) {
    aiOrchestrator = new WeedNearMeAIOrchestrator();
    await aiOrchestrator.initialize();
  }
  return aiOrchestrator;
}

export async function POST(request: NextRequest) {
  try {
    const { type, data } = await request.json();
    const ai = await getOrchestrator();
    
    switch (type) {
      case 'optimize-page': {
        // Generate GEO-optimized content for a specific page
        const geoContent = await ai.generateGEOSnippet(data);
        
        return NextResponse.json({ 
          success: true,
          content: geoContent,
          timestamp: new Date().toISOString()
        });
      }
      
      case 'analyze-competitors': {
        // Check competitor prices and content
        await ai.forceOptimization('pricing');
        
        return NextResponse.json({ 
          success: true,
          message: 'Competitor analysis completed',
          timestamp: new Date().toISOString()
        });
      }
      
      case 'track-llm-share': {
        // Monitor LLM responses
        await ai.forceOptimization('llm');
        
        return NextResponse.json({ 
          success: true,
          message: 'LLM share tracking completed',
          timestamp: new Date().toISOString()
        });
      }
      
      case 'full-optimization': {
        // Run complete optimization cycle
        await ai.forceOptimization('all');
        
        return NextResponse.json({ 
          success: true,
          message: 'Full optimization cycle completed',
          timestamp: new Date().toISOString()
        });
      }
      
      case 'model-status': {
        // Get current model status
        const status = await ai.getModelStatus();

        return NextResponse.json({
          success: true,
          models: status,
          timestamp: new Date().toISOString()
        });
      }
      
      default:
        return NextResponse.json(
          { success: false, error: 'Invalid operation type' },
          { status: 400 }
        );
    }
    
  } catch (error) {
    console.error('AI API Error:', error);
    
    return NextResponse.json(
      { 
        success: false, 
        error: 'AI processing failed',
        details: error instanceof Error ? error.message : 'Unknown error'
      },
      { status: 500 }
    );
  }
}

export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url);
    const operation = searchParams.get('operation');
    
    const ai = await getOrchestrator();
    
    switch (operation) {
      case 'status': {
        const status = await ai.getModelStatus();

        return NextResponse.json({
          success: true,
          models: status,
          isInitialized: true,
          timestamp: new Date().toISOString()
        });
      }
      
      case 'health': {
        const status = await ai.getModelStatus();
        const loadedCount = status.filter(m => m.status === 'ready').length;

        return NextResponse.json({
          success: true,
          status: loadedCount > 0 ? 'healthy' : 'degraded',
          modelsLoaded: loadedCount,
          totalModels: status.length,
          timestamp: new Date().toISOString()
        });
      }
      
      default:
        return NextResponse.json(
          { success: false, error: 'Invalid operation' },
          { status: 400 }
        );
    }
    
  } catch (error) {
    console.error('AI API GET Error:', error);
    
    return NextResponse.json(
      { 
        success: false, 
        error: 'AI status check failed',
        details: error instanceof Error ? error.message : 'Unknown error'
      },
      { status: 500 }
    );
  }
}
