export const runtime = 'nodejs'

import { NextRequest, NextResponse } from 'next/server'
import { prisma } from '@/lib/prisma'

export async function POST(request: NextRequest) {
  try {
    const body = await request.json()
    const { metrics, url, timestamp } = body

    // Validate required fields
    if (!metrics || !url || !timestamp) {
      return NextResponse.json(
        { error: 'Missing required fields' },
        { status: 400 }
      )
    }

    // Extract user information from headers
    const userAgent = request.headers.get('user-agent') || 'unknown'
    const ip = request.headers.get('x-forwarded-for') || 
               request.headers.get('x-real-ip') || 
               'unknown'

    // Store performance metrics in database
    await prisma.pageAnalytics.create({
      data: {
        url,
        pageLoadTime: metrics.pageLoadTime || null,
        firstContentfulPaint: metrics.firstContentfulPaint || null,
        largestContentfulPaint: metrics.largestContentfulPaint || null,
        firstInputDelay: metrics.firstInputDelay || null,
        cumulativeLayoutShift: metrics.cumulativeLayoutShift || null,
        timeToInteractive: metrics.timeToInteractive || null,
        totalBlockingTime: metrics.totalBlockingTime || null,
        deviceType: metrics.deviceType || 'unknown',
        connectionType: metrics.connectionType || 'unknown',
        userAgent,
        screenResolution: metrics.screenResolution || null,
        viewportSize: metrics.viewportSize || null,
        ipAddress: ip,
        timestamp: new Date(timestamp),
        createdAt: new Date()
      }
    })

    // Check performance budget violations
    const violations = checkPerformanceBudget(metrics)
    
    // Log violations for monitoring
    if (violations.length > 0) {
      console.warn('Performance budget violations:', {
        url,
        violations,
        metrics
      })
      
      // You could send alerts here for critical violations
      if (metrics.largestContentfulPaint > 4000) {
        // Critical LCP violation - could trigger alert
        console.error('Critical LCP violation:', metrics.largestContentfulPaint)
      }
    }

    return NextResponse.json({ 
      success: true,
      violations: violations.length > 0 ? violations : undefined
    })

  } catch (error) {
    console.error('Error storing performance metrics:', error)
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    )
  }
}

function checkPerformanceBudget(metrics: any): string[] {
  const budgets = {
    largestContentfulPaint: 2500, // 2.5s
    firstInputDelay: 100, // 100ms
    cumulativeLayoutShift: 0.1, // 0.1
    firstContentfulPaint: 1800, // 1.8s
    timeToInteractive: 3800, // 3.8s
    totalBlockingTime: 200 // 200ms
  }

  const violations: string[] = []

  Object.entries(budgets).forEach(([metric, budget]) => {
    const value = metrics[metric]
    if (value && value > budget) {
      violations.push(`${metric}: ${value} > ${budget}`)
    }
  })

  return violations
}

// GET endpoint for retrieving performance analytics
export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url)
    const days = parseInt(searchParams.get('days') || '7')
    const url = searchParams.get('url')

    const startDate = new Date()
    startDate.setDate(startDate.getDate() - days)

    const whereClause: any = {
      createdAt: {
        gte: startDate
      }
    }

    if (url) {
      whereClause.url = {
        contains: url
      }
    }

    const analytics = await prisma.pageAnalytics.findMany({
      where: whereClause,
      orderBy: {
        createdAt: 'desc'
      },
      take: 1000 // Limit results
    })

    // Calculate aggregated metrics
    const aggregated = calculateAggregatedMetrics(analytics)

    return NextResponse.json({
      success: true,
      data: analytics,
      aggregated,
      count: analytics.length
    })

  } catch (error) {
    console.error('Error retrieving performance analytics:', error)
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    )
  }
}

function calculateAggregatedMetrics(analytics: any[]) {
  if (analytics.length === 0) return null

  const metrics = {
    pageLoadTime: [],
    firstContentfulPaint: [],
    largestContentfulPaint: [],
    firstInputDelay: [],
    cumulativeLayoutShift: [],
    timeToInteractive: [],
    totalBlockingTime: []
  }

  analytics.forEach(record => {
    Object.keys(metrics).forEach(metric => {
      const value = record[metric]
      if (value !== null && value !== undefined) {
        metrics[metric as keyof typeof metrics].push(value)
      }
    })
  })

  const aggregated: any = {}

  Object.entries(metrics).forEach(([metric, values]) => {
    if (values.length > 0) {
      values.sort((a, b) => a - b)
      aggregated[metric] = {
        avg: values.reduce((sum, val) => sum + val, 0) / values.length,
        median: values[Math.floor(values.length / 2)],
        p75: values[Math.floor(values.length * 0.75)],
        p90: values[Math.floor(values.length * 0.90)],
        p95: values[Math.floor(values.length * 0.95)],
        min: values[0],
        max: values[values.length - 1]
      }
    }
  })

  return aggregated
}
