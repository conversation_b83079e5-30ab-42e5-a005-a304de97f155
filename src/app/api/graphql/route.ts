import { NextRequest, NextResponse } from 'next/server'

// Temporary GraphQL endpoint - will be properly implemented later
export async function GET(request: NextRequest) {
  return NextResponse.json({
    message: 'GraphQL endpoint is under construction',
    status: 'coming_soon'
  })
}

export async function POST(request: NextRequest) {
  return NextResponse.json({
    message: 'GraphQL endpoint is under construction',
    status: 'coming_soon'
  })
}
